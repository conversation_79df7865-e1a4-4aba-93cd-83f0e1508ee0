!function(n,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((n=n||self).Random={})}(this,function(n){"use strict";var t=9007199254740992,r=t-1,e=-1>>>0,i=e+1,o=i/2,u=o-1,f=1<<21,a=f-1;function c(n){return 0|n.next()}function s(n,t){return 0===t?n:function(r){return n(r)+t}}function h(n){var r=0|n.next(),e=n.next()>>>0;return(r&a)*i+e+(r&f?-t:0)}function p(n){for(;;){var r=0|n.next();if(!(4194304&r)){var e=n.next()>>>0;return(r&a)*i+e+(r&f?-t:0)}if(4194304==(8388607&r)&&0==(0|n.next()))return t}}function d(n){return n.next()>>>0}function l(n){var t=n.next()&a,r=n.next()>>>0;return t*i+r}function v(n){for(;;){var r=0|n.next();if(!(r&f)){var e=n.next()>>>0;return(r&a)*i+e}if(0==(r&a)&&0==(0|n.next()))return t}}function x(n){return 0==(n+1&n)}function g(n){return x(n)?(t=n,function(n){return n.next()&t}):function(n){var t=n+1,r=t*Math.floor(i/t);return function(n){var e=0;do{e=n.next()>>>0}while(e>=r);return e%t}}(n);var t}function y(n){var r,e=n+1;if(0==(0|e)){var o=(e/i|0)-1;if(x(o))return r=o,function(n){var t=n.next()&r,e=n.next()>>>0;return t*i+e}}return function(n){var r=n*Math.floor(t/n);return function(t){var e=0;do{var o=t.next()&a,u=t.next()>>>0;e=o*i+u}while(e>=r);return e%n}}(e)}function w(n,r){return function(e){var o=0;do{var u=0|e.next(),c=e.next()>>>0;o=(u&a)*i+c+(u&f?-t:0)}while(o<n||o>r);return o}}function m(n,i){if(n=Math.floor(n),i=Math.floor(i),n<-t||!isFinite(n))throw new RangeError("Expected min to be at least "+-t);if(i>t||!isFinite(i))throw new RangeError("Expected max to be at most "+t);var u=i-n;return u<=0||!isFinite(u)?function(){return n}:u===e?0===n?d:s(c,n+o):u<e?s(g(u),n):u===r?s(l,n):u<r?s(y(u),n):i-1-n===r?s(v,n):n===-t&&i===t?p:n===-t&&i===r?h:n===-r&&i===t?s(h,1):i===t?s(w(n-1,i-1),1):w(n,i)}function b(n){return 1==(1&n.next())}function E(n,t){return function(r){return n(r)<t}}function M(n,r){return null==r?null==n?b:function(n){if(n<=0)return function(){return!1};if(n>=1)return function(){return!0};var r=n*i;return r%1==0?E(c,r-o|0):E(l,Math.round(n*t))}(n):n<=0?function(){return!1}:n>=r?function(){return!0}:E(m(0,r-1),n)}function A(n,t){var r=m(+n,+t);return function(n){return new Date(r(n))}}function R(n){return m(1,n)}function F(n,t){var r=R(n);return function(n){for(var e=[],i=0;i<t;++i)e.push(r(n));return e}}var S="abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789_-";function C(n){void 0===n&&(n=S);var t=n.length;if(!t)throw new Error("Expected pool not to be an empty string");var r=m(0,t-1);return function(t,e){for(var i="",o=0;o<e;++o){var u=r(t);i+=n.charAt(u)}return i}}var k=C("0123456789abcdef"),I=C("0123456789abcdef".toUpperCase());function T(n){return n?I:k}function O(n,t){return n<0?Math.max(n+t,0):Math.min(n,t)}function W(n){var t=+n;return t<0?Math.ceil(t):Math.floor(t)}function Z(n,t,r,e){var i=t.length;if(0===i)throw new RangeError("Cannot pick from an empty array");var o=null==r?0:O(W(r),i),u=void 0===e?i:O(W(e),i);if(o>=u)throw new RangeError("Cannot pick between bounds "+o+" and "+u);return t[m(o,u-1)(n)]}function j(n){return l(n)/t}function B(n){return v(n)/t}function D(n,t,r){if(void 0===r&&(r=!1),!isFinite(n))throw new RangeError("Expected min to be a finite number");if(!isFinite(t))throw new RangeError("Expected max to be a finite number");return s((e=r?B:j,1===(i=t-n)?e:0===i?function(){return 0}:function(n){return e(n)*i}),n);var e,i}var U=Array.prototype.slice;function _(n,t,r){void 0===r&&(r=0);var e=t.length;if(e)for(var i=e-1>>>0;i>r;--i){var o=m(0,i)(n);if(i!==o){var u=t[i];t[i]=t[o],t[o]=u}}return t}function q(n,t,r){if(r<0||r>t.length||!isFinite(r))throw new RangeError("Expected sampleSize to be within 0 and the length of the population");if(0===r)return[];var e=U.call(t),i=e.length;if(i===r)return _(n,e,0);var o=i-r;return _(n,e,o-1).slice(o)}var z=function(){try{if("xxx"==="x".repeat(3))return function(n,t){return n.repeat(t)}}catch(n){}return function(n,t){for(var r="";t>0;)1&t&&(r+=n),t>>=1,n+=n;return r}}();function P(n,t){return z("0",t-n.length)+n}function V(n){var t=n.next()>>>0,r=0|n.next(),e=0|n.next(),i=n.next()>>>0;return P(t.toString(16),8)+"-"+P((65535&r).toString(16),4)+"-"+P((r>>4&4095|16384).toString(16),4)+"-"+P((16383&e|32768).toString(16),4)+"-"+P((e>>4&65535).toString(16),4)+P(i.toString(16),8)}var G={next:function(){return Math.random()*i|0}},H=function(){function n(n){void 0===n&&(n=G),this.engine=n}return n.prototype.int32=function(){return c(this.engine)},n.prototype.uint32=function(){return d(this.engine)},n.prototype.uint53=function(){return l(this.engine)},n.prototype.uint53Full=function(){return v(this.engine)},n.prototype.int53=function(){return h(this.engine)},n.prototype.int53Full=function(){return p(this.engine)},n.prototype.integer=function(n,t){return m(n,t)(this.engine)},n.prototype.realZeroToOneInclusive=function(){return B(this.engine)},n.prototype.realZeroToOneExclusive=function(){return j(this.engine)},n.prototype.real=function(n,t,r){return void 0===r&&(r=!1),D(n,t,r)(this.engine)},n.prototype.bool=function(n,t){return M(n,t)(this.engine)},n.prototype.pick=function(n,t,r){return Z(this.engine,n,t,r)},n.prototype.shuffle=function(n){return _(this.engine,n)},n.prototype.sample=function(n,t){return q(this.engine,n,t)},n.prototype.die=function(n){return R(n)(this.engine)},n.prototype.dice=function(n,t){return F(n,t)(this.engine)},n.prototype.uuid4=function(){return V(this.engine)},n.prototype.string=function(n,t){return C(t)(this.engine,n)},n.prototype.hex=function(n,t){return T(t)(this.engine,n)},n.prototype.date=function(n,t){return A(n,t)(this.engine)},n}(),J=function(){try{var n=new ArrayBuffer(4),t=new Int32Array(n);if(t[0]=o,t[0]===-o)return Int32Array}catch(n){}return Array}(),K=null,L=128,N={next:function(){return L>=128&&(null===K&&(K=new J(128)),crypto.getRandomValues(K),L=0),0|K[L++]}};function Q(n,t){void 0===n&&(n=G),void 0===t&&(t=16);var r=[];r.push(0|(new Date).getTime());for(var e=1;e<t;++e)r[e]=0|n.next();return r}var X=function(){try{if(-5===Math.imul(e,5))return Math.imul}catch(n){}return function(n,t){var r=65535&n,e=65535&t;return r*e+((n>>>16&65535)*e+r*(t>>>16&65535)<<16>>>0)|0}}(),Y=624,$=Y-1,nn=397,tn=Y-nn,rn=2567483615,en=function(){function n(){this.data=new J(Y),this.index=0,this.uses=0}return n.seed=function(t){return(new n).seed(t)},n.seedWithArray=function(t){return(new n).seedWithArray(t)},n.autoSeed=function(){return n.seedWithArray(Q())},n.prototype.next=function(){(0|this.index)>=Y&&(on(this.data),this.index=0);var n=this.data[this.index];return this.index=this.index+1|0,this.uses+=1,0|function(n){return n^=n>>>11,n^=n<<7&2636928640,(n^=n<<15&4022730752)^n>>>18}(n)},n.prototype.getUseCount=function(){return this.uses},n.prototype.discard=function(n){if(n<=0)return this;for(this.uses+=n,(0|this.index)>=Y&&(on(this.data),this.index=0);n+this.index>Y;)n-=Y-this.index,on(this.data),this.index=0;return this.index=this.index+n|0,this},n.prototype.seed=function(n){var t=0;this.data[0]=t=0|n;for(var r=1;r<Y;r=r+1|0)this.data[r]=t=X(t^t>>>30,1812433253)+r|0;return this.index=Y,this.uses=0,this},n.prototype.seedWithArray=function(n){return this.seed(19650218),function(n,t){for(var r=1,e=0,i=t.length,u=0|Math.max(i,Y),f=0|n[0];(0|u)>0;--u)n[r]=f=(n[r]^X(f^f>>>30,1664525))+(0|t[e])+(0|e)|0,++e,(0|(r=r+1|0))>$&&(n[0]=n[$],r=1),e>=i&&(e=0);for(u=$;(0|u)>0;--u)n[r]=f=(n[r]^X(f^f>>>30,1566083941))-r|0,(0|(r=r+1|0))>$&&(n[0]=n[$],r=1);n[0]=o}(this.data,n),this},n}();function on(n){for(var t=0,r=0;(0|t)<tn;t=t+1|0)r=n[t]&o|n[t+1|0]&u,n[t]=n[t+nn|0]^r>>>1^(1&r?rn:0);for(;(0|t)<$;t=t+1|0)r=n[t]&o|n[t+1|0]&u,n[t]=n[t-tn|0]^r>>>1^(1&r?rn:0);r=n[$]&o|n[0]&u,n[$]=n[nn-1]^r>>>1^(1&r?rn:0)}var un=null,fn=128,an={next:function(){return fn>=128&&(un=new Int32Array(new Int8Array(require("crypto").randomBytes(512)).buffer),fn=0),0|un[fn++]}};n.Random=H,n.browserCrypto=N,n.nativeMath=G,n.MersenneTwister19937=en,n.nodeCrypto=an,n.bool=M,n.date=A,n.dice=F,n.die=R,n.hex=T,n.int32=c,n.int53=h,n.int53Full=p,n.integer=m,n.pick=Z,n.picker=function(n,t,r){var e=U.call(n,t,r);if(0===e.length)throw new RangeError("Cannot pick from a source with no items");var i=m(0,e.length-1);return function(n){return e[i(n)]}},n.real=D,n.realZeroToOneExclusive=j,n.realZeroToOneInclusive=B,n.sample=q,n.shuffle=_,n.string=C,n.uint32=d,n.uint53=l,n.uint53Full=v,n.uuid4=V,n.createEntropy=Q,Object.defineProperty(n,"__esModule",{value:!0})});