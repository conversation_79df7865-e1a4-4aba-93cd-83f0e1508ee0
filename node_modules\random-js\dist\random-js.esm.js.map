{"version": 3, "file": "random-js.esm.js", "sources": ["../src/utils/constants.ts", "../src/distribution/int32.ts", "../src/utils/add.ts", "../src/distribution/int53.ts", "../src/distribution/int53Full.ts", "../src/distribution/uint32.ts", "../src/distribution/uint53.ts", "../src/distribution/uint53Full.ts", "../src/distribution/integer.ts", "../src/distribution/bool.ts", "../src/distribution/date.ts", "../src/distribution/die.ts", "../src/distribution/dice.ts", "../src/distribution/string.ts", "../src/distribution/hex.ts", "../src/utils/convertSliceArgument.ts", "../src/utils/toInteger.ts", "../src/distribution/pick.ts", "../src/utils/multiply.ts", "../src/distribution/realZeroToOneExclusive.ts", "../src/distribution/realZeroToOneInclusive.ts", "../src/distribution/real.ts", "../src/utils/sliceArray.ts", "../src/distribution/shuffle.ts", "../src/distribution/sample.ts", "../src/utils/stringRepeat.ts", "../src/distribution/uuid4.ts", "../src/engine/nativeMath.ts", "../src/Random.ts", "../src/utils/Int32Array.ts", "../src/engine/browserCrypto.ts", "../src/utils/createEntropy.ts", "../src/utils/imul.ts", "../src/engine/MersenneTwister19937.ts", "../src/engine/nodeCrypto.ts", "../src/distribution/picker.ts"], "sourcesContent": ["export const SMALLEST_UNSAFE_INTEGER = 0x20000000000000;\nexport const LARGEST_SAFE_INTEGER = SMALLEST_UNSAFE_INTEGER - 1;\nexport const UINT32_MAX = -1 >>> 0;\nexport const UINT32_SIZE = UINT32_MAX + 1;\nexport const INT32_SIZE = UINT32_SIZE / 2;\nexport const INT32_MAX = INT32_SIZE - 1;\nexport const UINT21_SIZE = 1 << 21;\nexport const UINT21_MAX = UINT21_SIZE - 1;\n", "import { Engine } from \"../types\";\n\n/**\n * Returns a value within [-0x80000000, 0x7fffffff]\n */\nexport function int32(engine: Engine): number {\n  return engine.next() | 0;\n}\n", "import { Distribution } from \"../types\";\n\nexport function add(distribution: Distribution, addend: number): Distribution {\n  if (addend === 0) {\n    return distribution;\n  } else {\n    return engine => distribution(engine) + addend;\n  }\n}\n", "import { Engine } from \"../types\";\nimport {\n  SMALLEST_UNSAFE_INTEGER,\n  UINT21_MAX,\n  UINT21_SIZE,\n  UINT32_SIZE\n} from \"../utils/constants\";\n\n/**\n * Returns a value within [-0x20000000000000, 0x1fffffffffffff]\n */\nexport function int53(engine: Engine): number {\n  const high = engine.next() | 0;\n  const low = engine.next() >>> 0;\n  return (\n    (high & UINT21_MAX) * UINT32_SIZE +\n    low +\n    (high & UINT21_SIZE ? -SMALLEST_UNSAFE_INTEGER : 0)\n  );\n}\n", "import { Engine } from \"../types\";\nimport {\n  SMALLEST_UNSAFE_INTEGER,\n  UINT21_MAX,\n  UINT21_SIZE,\n  UINT32_SIZE\n} from \"../utils/constants\";\n\n/**\n * Returns a value within [-0x20000000000000, 0x20000000000000]\n */\nexport function int53Full(engine: Engine): number {\n  while (true) {\n    const high = engine.next() | 0;\n    if (high & 0x400000) {\n      if ((high & 0x7fffff) === 0x400000 && (engine.next() | 0) === 0) {\n        return SMALLEST_UNSAFE_INTEGER;\n      }\n    } else {\n      const low = engine.next() >>> 0;\n      return (\n        (high & UINT21_MAX) * UINT32_SIZE +\n        low +\n        (high & UINT21_SIZE ? -SMALLEST_UNSAFE_INTEGER : 0)\n      );\n    }\n  }\n}\n", "import { Engine } from \"../types\";\n\n/**\n * Returns a value within [0, 0xffffffff]\n */\nexport function uint32(engine: Engine): number {\n  return engine.next() >>> 0;\n}\n", "import { Engine } from \"../types\";\nimport { UINT21_MAX, UINT32_SIZE } from \"../utils/constants\";\n\n/**\n * Returns a value within [0, 0x1fffffffffffff]\n */\nexport function uint53(engine: Engine): number {\n  const high = engine.next() & UINT21_MAX;\n  const low = engine.next() >>> 0;\n  return high * UINT32_SIZE + low;\n}\n", "import { Engine } from \"../types\";\nimport {\n  SMALLEST_UNSAFE_INTEGER,\n  UINT21_MAX,\n  UINT21_SIZE,\n  UINT32_SIZE\n} from \"../utils/constants\";\n\n/**\n * Returns a value within [0, 0x20000000000000]\n */\nexport function uint53Full(engine: Engine): number {\n  while (true) {\n    const high = engine.next() | 0;\n    if (high & UINT21_SIZE) {\n      if ((high & UINT21_MAX) === 0 && (engine.next() | 0) === 0) {\n        return SMALLEST_UNSAFE_INTEGER;\n      }\n    } else {\n      const low = engine.next() >>> 0;\n      return (high & UINT21_MAX) * UINT32_SIZE + low;\n    }\n  }\n}\n", "import { Distribution, Engine } from \"../types\";\nimport { add } from \"../utils/add\";\nimport {\n  INT32_SIZE,\n  LARGEST_SAFE_INTEGER,\n  SMALLEST_UNSAFE_INTEGER,\n  UINT21_MAX,\n  UINT21_SIZE,\n  UINT32_MAX,\n  UINT32_SIZE\n} from \"../utils/constants\";\nimport { int32 } from \"./int32\";\nimport { int53 } from \"./int53\";\nimport { int53Full } from \"./int53Full\";\nimport { uint32 } from \"./uint32\";\nimport { uint53 } from \"./uint53\";\nimport { uint53Full } from \"./uint53Full\";\n\nfunction isPowerOfTwoMinusOne(value: number): boolean {\n  return ((value + 1) & value) === 0;\n}\n\nfunction bitmask(masking: number): Distribution {\n  return (engine: Engine) => engine.next() & masking;\n}\n\nfunction downscaleToLoopCheckedRange(range: number): Distribution {\n  const extendedRange = range + 1;\n  const maximum = extendedRange * Math.floor(UINT32_SIZE / extendedRange);\n  return engine => {\n    let value = 0;\n    do {\n      value = engine.next() >>> 0;\n    } while (value >= maximum);\n    return value % extendedRange;\n  };\n}\n\nfunction downscaleToRange(range: number): Distribution {\n  if (isPowerOfTwoMinusOne(range)) {\n    return bitmask(range);\n  } else {\n    return downscaleToLoopCheckedRange(range);\n  }\n}\n\nfunction isEvenlyDivisibleByMaxInt32(value: number): boolean {\n  return (value | 0) === 0;\n}\n\nfunction upscaleWithHighMasking(masking: number): Distribution {\n  return engine => {\n    const high = engine.next() & masking;\n    const low = engine.next() >>> 0;\n    return high * UINT32_SIZE + low;\n  };\n}\n\nfunction upscaleToLoopCheckedRange(extendedRange: number): Distribution {\n  const maximum =\n    extendedRange * Math.floor(SMALLEST_UNSAFE_INTEGER / extendedRange);\n  return engine => {\n    let ret = 0;\n    do {\n      const high = engine.next() & UINT21_MAX;\n      const low = engine.next() >>> 0;\n      ret = high * UINT32_SIZE + low;\n    } while (ret >= maximum);\n    return ret % extendedRange;\n  };\n}\n\nfunction upscaleWithinU53(range: number): Distribution {\n  const extendedRange = range + 1;\n  if (isEvenlyDivisibleByMaxInt32(extendedRange)) {\n    const highRange = ((extendedRange / UINT32_SIZE) | 0) - 1;\n    if (isPowerOfTwoMinusOne(highRange)) {\n      return upscaleWithHighMasking(highRange);\n    }\n  }\n  return upscaleToLoopCheckedRange(extendedRange);\n}\n\nfunction upscaleWithinI53AndLoopCheck(min: number, max: number): Distribution {\n  return engine => {\n    let ret = 0;\n    do {\n      const high = engine.next() | 0;\n      const low = engine.next() >>> 0;\n      ret =\n        (high & UINT21_MAX) * UINT32_SIZE +\n        low +\n        (high & UINT21_SIZE ? -SMALLEST_UNSAFE_INTEGER : 0);\n    } while (ret < min || ret > max);\n    return ret;\n  };\n}\n\n/**\n * Returns a Distribution to return a value within [min, max]\n * @param min The minimum integer value, inclusive. No less than -0x20000000000000.\n * @param max The maximum integer value, inclusive. No greater than 0x20000000000000.\n */\nexport function integer(min: number, max: number): Distribution {\n  min = Math.floor(min);\n  max = Math.floor(max);\n  if (min < -SMALLEST_UNSAFE_INTEGER || !isFinite(min)) {\n    throw new RangeError(\n      `Expected min to be at least ${-SMALLEST_UNSAFE_INTEGER}`\n    );\n  } else if (max > SMALLEST_UNSAFE_INTEGER || !isFinite(max)) {\n    throw new RangeError(\n      `Expected max to be at most ${SMALLEST_UNSAFE_INTEGER}`\n    );\n  }\n\n  const range = max - min;\n  if (range <= 0 || !isFinite(range)) {\n    return () => min;\n  } else if (range === UINT32_MAX) {\n    if (min === 0) {\n      return uint32;\n    } else {\n      return add(int32, min + INT32_SIZE);\n    }\n  } else if (range < UINT32_MAX) {\n    return add(downscaleToRange(range), min);\n  } else if (range === LARGEST_SAFE_INTEGER) {\n    return add(uint53, min);\n  } else if (range < LARGEST_SAFE_INTEGER) {\n    return add(upscaleWithinU53(range), min);\n  } else if (max - 1 - min === LARGEST_SAFE_INTEGER) {\n    return add(uint53Full, min);\n  } else if (\n    min === -SMALLEST_UNSAFE_INTEGER &&\n    max === SMALLEST_UNSAFE_INTEGER\n  ) {\n    return int53Full;\n  } else if (min === -SMALLEST_UNSAFE_INTEGER && max === LARGEST_SAFE_INTEGER) {\n    return int53;\n  } else if (min === -LARGEST_SAFE_INTEGER && max === SMALLEST_UNSAFE_INTEGER) {\n    return add(int53, 1);\n  } else if (max === SMALLEST_UNSAFE_INTEGER) {\n    return add(upscaleWithinI53AndLoopCheck(min - 1, max - 1), 1);\n  } else {\n    return upscaleWithinI53AndLoopCheck(min, max);\n  }\n}\n", "import { Distribution, Engine } from \"../types\";\nimport { INT32_SIZE, SMALLEST_UNSAFE_INTEGER, UINT32_SIZE } from \"../utils/constants\";\nimport { int32 } from \"./int32\";\nimport { integer } from \"./integer\";\nimport { uint53 } from \"./uint53\";\n\nfunction isLeastBitTrue(engine: Engine) {\n  return (engine.next() & 1) === 1;\n}\n\nfunction lessThan(\n  distribution: Distribution,\n  value: number\n): Distribution<boolean> {\n  return engine => distribution(engine) < value;\n}\n\nfunction probability(percentage: number) {\n  if (percentage <= 0) {\n    return () => false;\n  } else if (percentage >= 1) {\n    return () => true;\n  } else {\n    const scaled = percentage * UINT32_SIZE;\n    if (scaled % 1 === 0) {\n      return lessThan(int32, (scaled - INT32_SIZE) | 0);\n    } else {\n      return lessThan(uint53, Math.round(percentage * SMALLEST_UNSAFE_INTEGER));\n    }\n  }\n}\n\n// tslint:disable:unified-signatures\n\n/**\n * Returns a boolean Distribution with 50% probability of being true or false\n */\nexport function bool(): Distribution<boolean>;\n/**\n * Returns a boolean Distribution with the provided `percentage` of being true\n * @param percentage A number within [0, 1] of how often the result should be `true`\n */\nexport function bool(percentage: number): Distribution<boolean>;\n/**\n * Returns a boolean Distribution with a probability of\n * `numerator` divided by `denominator` of being true\n * @param numerator The numerator of the probability\n * @param denominator The denominator of the probability\n */\nexport function bool(\n  numerator: number,\n  denominator: number\n): Distribution<boolean>;\nexport function bool(\n  numerator?: number,\n  denominator?: number\n): Distribution<boolean> {\n  if (denominator == null) {\n    if (numerator == null) {\n      return isLeastBitTrue;\n    }\n    return probability(numerator);\n  } else {\n    if (numerator! <= 0) {\n      return () => false;\n    } else if (numerator! >= denominator) {\n      return () => true;\n    }\n    return lessThan(integer(0, denominator - 1), numerator!);\n  }\n}\n", "import { Distribution } from \"../types\";\nimport { integer } from \"./integer\";\n\n/**\n * Returns a Distribution that returns a random `Date` within the inclusive\n * range of [`start`, `end`].\n * @param start The minimum `Date`\n * @param end The maximum `Date`\n */\nexport function date(start: Date, end: Date): Distribution<Date> {\n  const distribution = integer(+start, +end);\n  return engine => new Date(distribution(engine));\n}\n", "import { Distribution } from \"../types\";\nimport { integer } from \"./integer\";\n\n/**\n * Returns a Distribution to return a value within [1, sideCount]\n * @param sideCount The number of sides of the die\n */\nexport function die(sideCount: number): Distribution<number> {\n  return integer(1, sideCount);\n}\n", "import { Distribution } from \"../types\";\nimport { die } from \"./die\";\n\n/**\n * Returns a distribution that returns an array of length `dieCount` of values\n * within [1, `sideCount`]\n * @param sideCount The number of sides of each die\n * @param dieCount The number of dice\n */\nexport function dice(\n  sideCount: number,\n  dieCount: number\n): Distribution<number[]> {\n  const distribution = die(sideCount);\n  return engine => {\n    const result = [];\n    for (let i = 0; i < dieCount; ++i) {\n      result.push(distribution(engine));\n    }\n    return result;\n  };\n}\n", "import { StringDistribution } from \"../types\";\nimport { integer } from \"./integer\";\n\n// tslint:disable:unified-signatures\n\n// has 2**x chars, for faster uniform distribution\nconst DEFAULT_STRING_POOL =\n  \"abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789_-\";\n\n/**\n * Returns a distribution that returns a random string using numbers,\n * uppercase and lowercase letters, `_`, and `-` of length `length`.\n * @param length Length of the result string\n */\nexport function string(): StringDistribution;\n/**\n * Returns a distribution that returns a random string using the provided\n * string pool as the possible characters to choose from of length `length`.\n * @param length Length of the result string\n */\nexport function string(pool: string): StringDistribution;\nexport function string(pool: string = DEFAULT_STRING_POOL): StringDistribution {\n  const poolLength = pool.length;\n  if (!poolLength) {\n    throw new Error(\"Expected pool not to be an empty string\");\n  }\n\n  const distribution = integer(0, poolLength - 1);\n  return (engine, length) => {\n    let result = \"\";\n    for (let i = 0; i < length; ++i) {\n      const j = distribution(engine);\n      result += pool.charAt(j);\n    }\n    return result;\n  };\n}\n", "import { StringDistribution } from \"../types\";\nimport { string } from \"./string\";\n\nconst LOWER_HEX_POOL = \"0123456789abcdef\";\nconst lowerHex = string(LOWER_HEX_POOL);\nconst upperHex = string(LOWER_HEX_POOL.toUpperCase());\n\n/**\n * Returns a Distribution that returns a random string comprised of numbers\n * or the characters `abcdef` (or `ABCDEF`) of length `length`.\n * @param length Length of the result string\n * @param uppercase Whether the string should use `ABCDEF` instead of `abcdef`\n */\nexport function hex(uppercase?: boolean): StringDistribution {\n  if (uppercase) {\n    return upperHex;\n  } else {\n    return lowerHex;\n  }\n}\n", "export function convertSliceArgument(value: number, length: number): number {\n  if (value < 0) {\n    return Math.max(value + length, 0);\n  } else {\n    return Math.min(value, length);\n  }\n}\n", "export function toInteger(value: number) {\n  const num = +value;\n  if (num < 0) {\n    return Math.ceil(num);\n  } else {\n    return Math.floor(num);\n  }\n}\n", "import { Engine } from \"../types\";\nimport { convertSliceArgument } from \"../utils/convertSliceArgument\";\nimport { toInteger } from \"../utils/toInteger\";\nimport { integer } from \"./integer\";\n\n/**\n * Returns a random value within the provided `source` within the sliced\n * bounds of `begin` and `end`.\n * @param source an array of items to pick from\n * @param begin the beginning slice index (defaults to `0`)\n * @param end the ending slice index (defaults to `source.length`)\n */\nexport function pick<T>(\n  engine: Engine,\n  source: ArrayLike<T>,\n  begin?: number,\n  end?: number\n): T {\n  const length = source.length;\n  if (length === 0) {\n    throw new RangeError(\"Cannot pick from an empty array\");\n  }\n  const start =\n    begin == null ? 0 : convertSliceArgument(toInteger(begin), length);\n  const finish =\n    end === void 0 ? length : convertSliceArgument(toInteger(end), length);\n  if (start >= finish) {\n    throw new RangeError(`Cannot pick between bounds ${start} and ${finish}`);\n  }\n  const distribution = integer(start, finish - 1);\n  return source[distribution(engine)];\n}\n", "import { Distribution } from \"../types\";\n\nexport function multiply(\n  distribution: Distribution,\n  multiplier: number\n): Distribution {\n  if (multiplier === 1) {\n    return distribution;\n  } else if (multiplier === 0) {\n    return () => 0;\n  } else {\n    return engine => distribution(engine) * multiplier;\n  }\n}\n", "import { Engine } from \"../types\";\nimport { SMALLEST_UNSAFE_INTEGER } from \"../utils/constants\";\nimport { uint53 } from \"./uint53\";\n\n/**\n * Returns a floating-point value within [0.0, 1.0)\n */\nexport function realZeroToOneExclusive(engine: Engine): number {\n  return uint53(engine) / SMALLEST_UNSAFE_INTEGER;\n}\n", "import { Engine } from \"../types\";\nimport { SMALLEST_UNSAFE_INTEGER } from \"../utils/constants\";\nimport { uint53Full } from \"./uint53Full\";\n\n/**\n * Returns a floating-point value within [0.0, 1.0]\n */\nexport function realZeroToOneInclusive(engine: Engine): number {\n  return uint53Full(engine) / SMALLEST_UNSAFE_INTEGER;\n}\n", "import { Distribution } from \"../types\";\nimport { add } from \"../utils/add\";\nimport { multiply } from \"../utils/multiply\";\nimport { realZeroToOneExclusive } from \"./realZeroToOneExclusive\";\nimport { realZeroToOneInclusive } from \"./realZeroToOneInclusive\";\n\n/**\n * Returns a floating-point value within [min, max) or [min, max]\n * @param min The minimum floating-point value, inclusive.\n * @param max The maximum floating-point value.\n * @param inclusive If true, `max` will be inclusive.\n */\nexport function real(\n  min: number,\n  max: number,\n  inclusive: boolean = false\n): Distribution {\n  if (!isFinite(min)) {\n    throw new RangeError(\"Expected min to be a finite number\");\n  } else if (!isFinite(max)) {\n    throw new RangeError(\"Expected max to be a finite number\");\n  }\n  return add(\n    multiply(\n      inclusive ? realZeroToOneInclusive : realZeroToOneExclusive,\n      max - min\n    ),\n    min\n  );\n}\n", "export const sliceArray = Array.prototype.slice;\n", "import { Engine } from \"../types\";\nimport { integer } from \"./integer\";\n\n/**\n * Shuffles an array in-place\n * @param engine The Engine to use when choosing random values\n * @param array The array to shuffle\n * @param downTo minimum index to shuffle. Only used internally.\n */\nexport function shuffle<T>(\n  engine: Engine,\n  array: T[],\n  downTo: number = 0\n): T[] {\n  const length = array.length;\n  if (length) {\n    for (let i = (length - 1) >>> 0; i > downTo; --i) {\n      const distribution = integer(0, i);\n      const j = distribution(engine);\n      if (i !== j) {\n        const tmp = array[i];\n        array[i] = array[j];\n        array[j] = tmp;\n      }\n    }\n  }\n  return array;\n}\n", "import { Engine } from \"../types\";\nimport { sliceArray } from \"../utils/sliceArray\";\nimport { shuffle } from \"./shuffle\";\n\n/**\n * From the population array, produce an array with sampleSize elements that\n * are randomly chosen without repeats.\n * @param engine The Engine to use when choosing random values\n * @param population An array that has items to choose a sample from\n * @param sampleSize The size of the result array\n */\nexport function sample<T>(\n  engine: Engine,\n  population: ArrayLike<T>,\n  sampleSize: number\n): T[] {\n  if (\n    sampleSize < 0 ||\n    sampleSize > population.length ||\n    !isFinite(sampleSize)\n  ) {\n    throw new RangeError(\n      \"Expected sampleSize to be within 0 and the length of the population\"\n    );\n  }\n\n  if (sampleSize === 0) {\n    return [];\n  }\n\n  const clone = sliceArray.call(population);\n  const length = clone.length;\n  if (length === sampleSize) {\n    return shuffle(engine, clone, 0);\n  }\n  const tailLength = length - sampleSize;\n  return shuffle(engine, clone, tailLength - 1).slice(tailLength);\n}\n", "export const stringRepeat = (() => {\n  try {\n    if ((\"x\" as any).repeat(3) === \"xxx\") {\n      return (pattern: string, count: number): string =>\n        (pattern as any).repeat(count);\n    }\n  } catch (_) {\n    // nothing to do here\n  }\n  return (pattern: string, count: number): string => {\n    let result = \"\";\n    while (count > 0) {\n      if (count & 1) {\n        result += pattern;\n      }\n      count >>= 1;\n      pattern += pattern;\n    }\n    return result;\n  };\n})();\n", "import { Engine } from \"../types\";\nimport { stringRepeat } from \"../utils/stringRepeat\";\n\nfunction zeroPad(text: string, zeroCount: number) {\n  return stringRepeat(\"0\", zeroCount - text.length) + text;\n}\n\n/**\n * Returns a Universally Unique Identifier Version 4.\n *\n * See http://en.wikipedia.org/wiki/Universally_unique_identifier\n */\nexport function uuid4(engine: Engine) {\n  const a = engine.next() >>> 0;\n  const b = engine.next() | 0;\n  const c = engine.next() | 0;\n  const d = engine.next() >>> 0;\n\n  return (\n    zeroPad(a.toString(16), 8) +\n    \"-\" +\n    zeroPad((b & 0xffff).toString(16), 4) +\n    \"-\" +\n    zeroPad((((b >> 4) & 0x0fff) | 0x4000).toString(16), 4) +\n    \"-\" +\n    zeroPad(((c & 0x3fff) | 0x8000).toString(16), 4) +\n    \"-\" +\n    zeroPad(((c >> 4) & 0xffff).toString(16), 4) +\n    zeroPad(d.toString(16), 8)\n  );\n}\n", "import { Engine } from \"../types\";\nimport { UINT32_SIZE } from \"../utils/constants\";\n\n/**\n * An int32-producing Engine that uses `Math.random()`\n */\nexport const nativeMath: Engine = {\n  next() {\n    return (Math.random() * UINT32_SIZE) | 0;\n  }\n};\n", "import { bool } from \"./distribution/bool\";\nimport { date } from \"./distribution/date\";\nimport { dice } from \"./distribution/dice\";\nimport { die } from \"./distribution/die\";\nimport { hex } from \"./distribution/hex\";\nimport { int32 } from \"./distribution/int32\";\nimport { int53 } from \"./distribution/int53\";\nimport { int53Full } from \"./distribution/int53Full\";\nimport { integer } from \"./distribution/integer\";\nimport { pick } from \"./distribution/pick\";\nimport { real } from \"./distribution/real\";\nimport { realZeroToOneExclusive } from \"./distribution/realZeroToOneExclusive\";\nimport { realZeroToOneInclusive } from \"./distribution/realZeroToOneInclusive\";\nimport { sample } from \"./distribution/sample\";\nimport { shuffle } from \"./distribution/shuffle\";\nimport { string } from \"./distribution/string\";\nimport { uint32 } from \"./distribution/uint32\";\nimport { uint53 } from \"./distribution/uint53\";\nimport { uint53Full } from \"./distribution/uint53Full\";\nimport { uuid4 } from \"./distribution/uuid4\";\nimport { nativeMath } from \"./engine/nativeMath\";\nimport { Engine } from \"./types\";\n\n// tslint:disable:unified-signatures\n\n/**\n * A wrapper around an Engine that provides easy-to-use methods for\n * producing values based on known distributions\n */\nexport class Random {\n  private readonly engine: Engine;\n\n  /**\n   * Creates a new Random wrapper\n   * @param engine The engine to use (defaults to a `Math.random`-based implementation)\n   */\n  constructor(engine: Engine = nativeMath) {\n    this.engine = engine;\n  }\n\n  /**\n   * Returns a value within [-0x80000000, 0x7fffffff]\n   */\n  public int32(): number {\n    return int32(this.engine);\n  }\n\n  /**\n   * Returns a value within [0, 0xffffffff]\n   */\n  public uint32(): number {\n    return uint32(this.engine);\n  }\n\n  /**\n   * Returns a value within [0, 0x1fffffffffffff]\n   */\n  public uint53(): number {\n    return uint53(this.engine);\n  }\n\n  /**\n   * Returns a value within [0, 0x20000000000000]\n   */\n  public uint53Full(): number {\n    return uint53Full(this.engine);\n  }\n\n  /**\n   * Returns a value within [-0x20000000000000, 0x1fffffffffffff]\n   */\n  public int53(): number {\n    return int53(this.engine);\n  }\n\n  /**\n   * Returns a value within [-0x20000000000000, 0x20000000000000]\n   */\n  public int53Full(): number {\n    return int53Full(this.engine);\n  }\n\n  /**\n   * Returns a value within [min, max]\n   * @param min The minimum integer value, inclusive. No less than -0x20000000000000.\n   * @param max The maximum integer value, inclusive. No greater than 0x20000000000000.\n   */\n  public integer(min: number, max: number): number {\n    return integer(min, max)(this.engine);\n  }\n\n  /**\n   * Returns a floating-point value within [0.0, 1.0]\n   */\n  public realZeroToOneInclusive(): number {\n    return realZeroToOneInclusive(this.engine);\n  }\n\n  /**\n   * Returns a floating-point value within [0.0, 1.0)\n   */\n  public realZeroToOneExclusive(): number {\n    return realZeroToOneExclusive(this.engine);\n  }\n\n  /**\n   * Returns a floating-point value within [min, max) or [min, max]\n   * @param min The minimum floating-point value, inclusive.\n   * @param max The maximum floating-point value.\n   * @param inclusive If true, `max` will be inclusive.\n   */\n  public real(min: number, max: number, inclusive: boolean = false): number {\n    return real(min, max, inclusive)(this.engine);\n  }\n\n  /**\n   * Returns a boolean with 50% probability of being true or false\n   */\n  public bool(): boolean;\n  /**\n   * Returns a boolean with the provided `percentage` of being true\n   * @param percentage A number within [0, 1] of how often the result should be `true`\n   */\n  public bool(percentage: number): boolean;\n  /**\n   * Returns a boolean with a probability of `numerator`/`denominator` of being true\n   * @param numerator The numerator of the probability\n   * @param denominator The denominator of the probability\n   */\n  public bool(numerator: number, denominator: number): boolean;\n  public bool(numerator?: number, denominator?: number): boolean {\n    return bool(numerator!, denominator!)(this.engine);\n  }\n\n  /**\n   * Return a random value within the provided `source` within the sliced\n   * bounds of `begin` and `end`.\n   * @param source an array of items to pick from\n   * @param begin the beginning slice index (defaults to `0`)\n   * @param end the ending slice index (defaults to `source.length`)\n   */\n  public pick<T>(source: ArrayLike<T>, begin?: number, end?: number): T {\n    return pick(this.engine, source, begin, end);\n  }\n\n  /**\n   * Shuffles an array in-place\n   * @param array The array to shuffle\n   */\n  public shuffle<T>(array: T[]): T[] {\n    return shuffle(this.engine, array);\n  }\n\n  /**\n   * From the population array, returns an array with sampleSize elements that\n   * are randomly chosen without repeats.\n   * @param population An array that has items to choose a sample from\n   * @param sampleSize The size of the result array\n   */\n  public sample<T>(population: ArrayLike<T>, sampleSize: number): T[] {\n    return sample(this.engine, population, sampleSize);\n  }\n\n  /**\n   * Returns a value within [1, sideCount]\n   * @param sideCount The number of sides of the die\n   */\n  public die(sideCount: number): number {\n    return die(sideCount)(this.engine);\n  }\n\n  /**\n   * Returns an array of length `dieCount` of values within [1, sideCount]\n   * @param sideCount The number of sides of each die\n   * @param dieCount The number of dice\n   */\n  public dice(sideCount: number, dieCount: number): number[] {\n    return dice(sideCount, dieCount)(this.engine);\n  }\n\n  /**\n   * Returns a Universally Unique Identifier Version 4.\n   *\n   * See http://en.wikipedia.org/wiki/Universally_unique_identifier\n   */\n  public uuid4(): string {\n    return uuid4(this.engine);\n  }\n\n  /**\n   * Returns a random string using numbers, uppercase and lowercase letters,\n   * `_`, and `-` of length `length`.\n   * @param length Length of the result string\n   */\n  public string(length: number): string;\n  /**\n   * Returns a random string using the provided string pool as the possible\n   * characters to choose from of length `length`.\n   * @param length Length of the result string\n   */\n  public string(length: number, pool: string): string;\n  public string(length: number, pool?: string): string {\n    return string(pool!)(this.engine, length);\n  }\n\n  /**\n   * Returns a random string comprised of numbers or the characters `abcdef`\n   * (or `ABCDEF`) of length `length`.\n   * @param length Length of the result string\n   * @param uppercase Whether the string should use `ABCDEF` instead of `abcdef`\n   */\n  public hex(length: number, uppercase?: boolean): string {\n    return hex(uppercase)(this.engine, length);\n  }\n\n  /**\n   * Returns a random `Date` within the inclusive range of [`start`, `end`].\n   * @param start The minimum `Date`\n   * @param end The maximum `Date`\n   */\n  public date(start: Date, end: Date): Date {\n    return date(start, end)(this.engine);\n  }\n}\n", "import { INT32_SIZE } from \"./constants\";\n\n/**\n * See https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Int32Array\n */\nconst I32Array: typeof Int32Array = (() => {\n  try {\n    const buffer = new ArrayBuffer(4);\n    const view = new Int32Array(buffer);\n    view[0] = INT32_SIZE;\n    if (view[0] === -INT32_SIZE) {\n      return Int32Array;\n    }\n  } catch (_) {\n    // nothing to do here\n  }\n  return (Array as unknown) as typeof Int32Array;\n})();\nexport { I32Array as Int32Array };\n", "import { Engine } from \"../types\";\nimport { Int32Array } from \"../utils/Int32Array\";\n\nlet data: Int32Array | null = null;\nconst COUNT = 128;\nlet index = COUNT;\n\n/**\n * An Engine that relies on the globally-available `crypto.getRandomValues`,\n * which is typically available in modern browsers.\n *\n * See https://developer.mozilla.org/en-US/docs/Web/API/Crypto/getRandomValues\n *\n * If unavailable or otherwise non-functioning, then `browserCrypto` will\n * likely `throw` on the first call to `next()`.\n */\nexport const browserCrypto: Engine = {\n  next() {\n    if (index >= COUNT) {\n      if (data === null) {\n        data = new Int32Array(COUNT);\n      }\n      crypto.getRandomValues(data);\n      index = 0;\n    }\n    return data![index++] | 0;\n  }\n};\n", "import { nativeMath } from \"../engine/nativeMath\";\nimport { Engine } from \"../types\";\n\n/**\n * Returns an array of random int32 values, based on current time\n * and a random number engine\n *\n * @param engine an Engine to pull random values from, default `nativeMath`\n * @param length the length of the Array, minimum 1, default 16\n */\nexport function createEntropy(\n  engine: Engine = nativeMath,\n  length: number = 16\n): number[] {\n  const array: number[] = [];\n  array.push(new Date().getTime() | 0);\n  for (let i = 1; i < length; ++i) {\n    array[i] = engine.next() | 0;\n  }\n  return array;\n}\n", "import { UINT32_MAX } from \"./constants\";\n\n/**\n * See https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Math/imul\n */\nexport const imul: (a: number, b: number) => number = (() => {\n  try {\n    if ((Math as any).imul(UINT32_MAX, 5) === -5) {\n      return (Math as any).imul;\n    }\n  } catch (_) {\n    // nothing to do here\n  }\n  const UINT16_MAX = 0xffff;\n  return (a: number, b: number) => {\n    const ah = (a >>> 16) & UINT16_MAX;\n    const al = a & UINT16_MAX;\n    const bh = (b >>> 16) & UINT16_MAX;\n    const bl = b & UINT16_MAX;\n    // the shift by 0 fixes the sign on the high part\n    // the final |0 converts the unsigned value into a signed value\n    return (al * bl + (((ah * bl + al * bh) << 16) >>> 0)) | 0;\n  };\n})();\n", "import { Engine } from \"../types\";\nimport { INT32_MAX, INT32_SIZE } from \"../utils/constants\";\nimport { createEntropy } from \"../utils/createEntropy\";\nimport { imul } from \"../utils/imul\";\nimport { Int32Array } from \"../utils/Int32Array\";\n\nconst ARRAY_SIZE = 624;\nconst ARRAY_MAX = ARRAY_SIZE - 1;\nconst M = 397;\nconst ARRAY_SIZE_MINUS_M = ARRAY_SIZE - M;\nconst A = 0x9908b0df;\n\n/**\n * An Engine that is a pseudorandom number generator using the Mersenne\n * Twister algorithm based on the prime 2**19937 − 1\n *\n * See http://en.wikipedia.org/wiki/Mersenne_twister\n */\nexport class MersenneTwister19937 implements Engine {\n  /**\n   * Returns a MersenneTwister19937 seeded with an initial int32 value\n   * @param initial the initial seed value\n   */\n  public static seed(initial: number): MersenneTwister19937 {\n    return new MersenneTwister19937().seed(initial);\n  }\n\n  /**\n   * Returns a MersenneTwister19937 seeded with zero or more int32 values\n   * @param source A series of int32 values\n   */\n  public static seedWithArray(source: ArrayLike<number>): MersenneTwister19937 {\n    return new MersenneTwister19937().seedWithArray(source);\n  }\n\n  /**\n   * Returns a MersenneTwister19937 seeded with the current time and\n   * a series of natively-generated random values\n   */\n  public static autoSeed(): MersenneTwister19937 {\n    return MersenneTwister19937.seedWithArray(createEntropy());\n  }\n\n  private readonly data = new Int32Array(ARRAY_SIZE);\n  private index = 0; // integer within [0, 624]\n  private uses = 0;\n\n  /**\n   * MersenneTwister19937 should not be instantiated directly.\n   * Instead, use the static methods `seed`, `seedWithArray`, or `autoSeed`.\n   */\n  private constructor() {}\n\n  /**\n   * Returns the next int32 value of the sequence\n   */\n  public next(): number {\n    if ((this.index | 0) >= ARRAY_SIZE) {\n      refreshData(this.data);\n      this.index = 0;\n    }\n\n    const value = this.data[this.index];\n    this.index = (this.index + 1) | 0;\n    this.uses += 1;\n    return temper(value) | 0;\n  }\n\n  /**\n   * Returns the number of times that the Engine has been used.\n   *\n   * This can be provided to an unused MersenneTwister19937 with the same\n   * seed, bringing it to the exact point that was left off.\n   */\n  public getUseCount(): number {\n    return this.uses;\n  }\n\n  /**\n   * Discards one or more items from the engine\n   * @param count The count of items to discard\n   */\n  public discard(count: number): this {\n    if (count <= 0) {\n      return this;\n    }\n    this.uses += count;\n    if ((this.index | 0) >= ARRAY_SIZE) {\n      refreshData(this.data);\n      this.index = 0;\n    }\n    while (count + this.index > ARRAY_SIZE) {\n      count -= ARRAY_SIZE - this.index;\n      refreshData(this.data);\n      this.index = 0;\n    }\n    this.index = (this.index + count) | 0;\n    return this;\n  }\n\n  private seed(initial: number): this {\n    let previous = 0;\n    this.data[0] = previous = initial | 0;\n\n    for (let i = 1; i < ARRAY_SIZE; i = (i + 1) | 0) {\n      this.data[i] = previous =\n        (imul(previous ^ (previous >>> 30), 0x6c078965) + i) | 0;\n    }\n    this.index = ARRAY_SIZE;\n    this.uses = 0;\n    return this;\n  }\n\n  private seedWithArray(source: ArrayLike<number>): this {\n    this.seed(0x012bd6aa);\n    seedWithArray(this.data, source);\n    return this;\n  }\n}\n\nfunction refreshData(data: Int32Array) {\n  let k = 0;\n  let tmp = 0;\n  for (; (k | 0) < ARRAY_SIZE_MINUS_M; k = (k + 1) | 0) {\n    tmp = (data[k] & INT32_SIZE) | (data[(k + 1) | 0] & INT32_MAX);\n    data[k] = data[(k + M) | 0] ^ (tmp >>> 1) ^ (tmp & 0x1 ? A : 0);\n  }\n\n  for (; (k | 0) < ARRAY_MAX; k = (k + 1) | 0) {\n    tmp = (data[k] & INT32_SIZE) | (data[(k + 1) | 0] & INT32_MAX);\n    data[k] =\n      data[(k - ARRAY_SIZE_MINUS_M) | 0] ^ (tmp >>> 1) ^ (tmp & 0x1 ? A : 0);\n  }\n\n  tmp = (data[ARRAY_MAX] & INT32_SIZE) | (data[0] & INT32_MAX);\n  data[ARRAY_MAX] = data[M - 1] ^ (tmp >>> 1) ^ (tmp & 0x1 ? A : 0);\n}\n\nfunction temper(value: number) {\n  value ^= value >>> 11;\n  value ^= (value << 7) & 0x9d2c5680;\n  value ^= (value << 15) & 0xefc60000;\n  return value ^ (value >>> 18);\n}\n\nfunction seedWithArray(data: Int32Array, source: ArrayLike<number>) {\n  let i = 1;\n  let j = 0;\n  const sourceLength = source.length;\n  let k = Math.max(sourceLength, ARRAY_SIZE) | 0;\n  let previous = data[0] | 0;\n  for (; (k | 0) > 0; --k) {\n    data[i] = previous =\n      ((data[i] ^ imul(previous ^ (previous >>> 30), 0x0019660d)) +\n        (source[j] | 0) +\n        (j | 0)) |\n      0;\n    i = (i + 1) | 0;\n    ++j;\n    if ((i | 0) > ARRAY_MAX) {\n      data[0] = data[ARRAY_MAX];\n      i = 1;\n    }\n    if (j >= sourceLength) {\n      j = 0;\n    }\n  }\n  for (k = ARRAY_MAX; (k | 0) > 0; --k) {\n    data[i] = previous =\n      ((data[i] ^ imul(previous ^ (previous >>> 30), 0x5d588b65)) - i) | 0;\n    i = (i + 1) | 0;\n    if ((i | 0) > ARRAY_MAX) {\n      data[0] = data[ARRAY_MAX];\n      i = 1;\n    }\n  }\n  data[0] = INT32_SIZE;\n}\n", "import { Engine } from \"../types\";\n\nlet data: Int32Array | null = null;\nconst COUNT = 128;\nlet index = COUNT;\n\n/**\n * An Engine that relies on the node-available\n * `require('crypto').randomBytes`, which has been available since 0.58.\n *\n * See https://nodejs.org/api/crypto.html#crypto_crypto_randombytes_size_callback\n *\n * If unavailable or otherwise non-functioning, then `nodeCrypto` will\n * likely `throw` on the first call to `next()`.\n */\nexport const nodeCrypto: Engine = {\n  next() {\n    if (index >= COUNT) {\n      data = new Int32Array(\n        new Int8Array(require(\"crypto\").randomBytes(4 * COUNT)).buffer\n      );\n      index = 0;\n    }\n    return data![index++] | 0;\n  }\n};\n", "import { Distribution } from \"../types\";\nimport { sliceArray } from \"../utils/sliceArray\";\nimport { integer } from \"./integer\";\n\n/**\n * Returns a Distribution to random value within the provided `source`\n * within the sliced bounds of `begin` and `end`.\n * @param source an array of items to pick from\n * @param begin the beginning slice index (defaults to `0`)\n * @param end the ending slice index (defaults to `source.length`)\n */\nexport function picker<T>(\n  source: ArrayLike<T>,\n  begin?: number,\n  end?: number\n): Distribution<T> {\n  const clone = sliceArray.call(source, begin, end);\n  if (clone.length === 0) {\n    throw new RangeError(`Cannot pick from a source with no items`);\n  }\n  const distribution = integer(0, clone.length - 1);\n  return engine => clone[distribution(engine)];\n}\n"], "names": ["Int32Array", "data", "COUNT", "index"], "mappings": "AAAO,MAAM,uBAAuB,GAAG,gBAAgB,CAAC;AACxD,AAAO,MAAM,oBAAoB,GAAG,uBAAuB,GAAG,CAAC,CAAC;AAChE,AAAO,MAAM,UAAU,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC;AACnC,AAAO,MAAM,WAAW,GAAG,UAAU,GAAG,CAAC,CAAC;AAC1C,AAAO,MAAM,UAAU,GAAG,WAAW,GAAG,CAAC,CAAC;AAC1C,AAAO,MAAM,SAAS,GAAG,UAAU,GAAG,CAAC,CAAC;AACxC,AAAO,MAAM,WAAW,GAAG,CAAC,IAAI,EAAE,CAAC;AACnC,AAAO,MAAM,UAAU,GAAG,WAAW,GAAG,CAAC,CAAC;;ACL1C;;;AAGA,SAAgB,KAAK,CAAC,MAAc;IAClC,OAAO,MAAM,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;CAC1B;;SCLe,GAAG,CAAC,YAA0B,EAAE,MAAc;IAC5D,IAAI,MAAM,KAAK,CAAC,EAAE;QAChB,OAAO,YAAY,CAAC;KACrB;SAAM;QACL,OAAO,MAAM,IAAI,YAAY,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC;KAChD;CACF;;ACAD;;;AAGA,SAAgB,KAAK,CAAC,MAAc;IAClC,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;IAC/B,MAAM,GAAG,GAAG,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IAChC,QACE,CAAC,IAAI,GAAG,UAAU,IAAI,WAAW;QACjC,GAAG;SACF,IAAI,GAAG,WAAW,GAAG,CAAC,uBAAuB,GAAG,CAAC,CAAC,EACnD;CACH;;ACXD;;;AAGA,SAAgB,SAAS,CAAC,MAAc;IACtC,OAAO,IAAI,EAAE;QACX,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;QAC/B,IAAI,IAAI,GAAG,QAAQ,EAAE;YACnB,IAAI,CAAC,IAAI,GAAG,QAAQ,MAAM,QAAQ,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,GAAG,CAAC,MAAM,CAAC,EAAE;gBAC/D,OAAO,uBAAuB,CAAC;aAChC;SACF;aAAM;YACL,MAAM,GAAG,GAAG,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;YAChC,QACE,CAAC,IAAI,GAAG,UAAU,IAAI,WAAW;gBACjC,GAAG;iBACF,IAAI,GAAG,WAAW,GAAG,CAAC,uBAAuB,GAAG,CAAC,CAAC,EACnD;SACH;KACF;CACF;;ACzBD;;;AAGA,SAAgB,MAAM,CAAC,MAAc;IACnC,OAAO,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;CAC5B;;ACJD;;;AAGA,SAAgB,MAAM,CAAC,MAAc;IACnC,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,EAAE,GAAG,UAAU,CAAC;IACxC,MAAM,GAAG,GAAG,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IAChC,OAAO,IAAI,GAAG,WAAW,GAAG,GAAG,CAAC;CACjC;;ACFD;;;AAGA,SAAgB,UAAU,CAAC,MAAc;IACvC,OAAO,IAAI,EAAE;QACX,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;QAC/B,IAAI,IAAI,GAAG,WAAW,EAAE;YACtB,IAAI,CAAC,IAAI,GAAG,UAAU,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,GAAG,CAAC,MAAM,CAAC,EAAE;gBAC1D,OAAO,uBAAuB,CAAC;aAChC;SACF;aAAM;YACL,MAAM,GAAG,GAAG,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;YAChC,OAAO,CAAC,IAAI,GAAG,UAAU,IAAI,WAAW,GAAG,GAAG,CAAC;SAChD;KACF;CACF;;ACLD,SAAS,oBAAoB,CAAC,KAAa;IACzC,OAAO,CAAC,CAAC,KAAK,GAAG,CAAC,IAAI,KAAK,MAAM,CAAC,CAAC;CACpC;AAED,SAAS,OAAO,CAAC,OAAe;IAC9B,OAAO,CAAC,MAAc,KAAK,MAAM,CAAC,IAAI,EAAE,GAAG,OAAO,CAAC;CACpD;AAED,SAAS,2BAA2B,CAAC,KAAa;IAChD,MAAM,aAAa,GAAG,KAAK,GAAG,CAAC,CAAC;IAChC,MAAM,OAAO,GAAG,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,GAAG,aAAa,CAAC,CAAC;IACxE,OAAO,MAAM;QACX,IAAI,KAAK,GAAG,CAAC,CAAC;QACd,GAAG;YACD,KAAK,GAAG,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;SAC7B,QAAQ,KAAK,IAAI,OAAO,EAAE;QAC3B,OAAO,KAAK,GAAG,aAAa,CAAC;KAC9B,CAAC;CACH;AAED,SAAS,gBAAgB,CAAC,KAAa;IACrC,IAAI,oBAAoB,CAAC,KAAK,CAAC,EAAE;QAC/B,OAAO,OAAO,CAAC,KAAK,CAAC,CAAC;KACvB;SAAM;QACL,OAAO,2BAA2B,CAAC,KAAK,CAAC,CAAC;KAC3C;CACF;AAED,SAAS,2BAA2B,CAAC,KAAa;IAChD,OAAO,CAAC,KAAK,GAAG,CAAC,MAAM,CAAC,CAAC;CAC1B;AAED,SAAS,sBAAsB,CAAC,OAAe;IAC7C,OAAO,MAAM;QACX,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,EAAE,GAAG,OAAO,CAAC;QACrC,MAAM,GAAG,GAAG,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QAChC,OAAO,IAAI,GAAG,WAAW,GAAG,GAAG,CAAC;KACjC,CAAC;CACH;AAED,SAAS,yBAAyB,CAAC,aAAqB;IACtD,MAAM,OAAO,GACX,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,uBAAuB,GAAG,aAAa,CAAC,CAAC;IACtE,OAAO,MAAM;QACX,IAAI,GAAG,GAAG,CAAC,CAAC;QACZ,GAAG;YACD,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,EAAE,GAAG,UAAU,CAAC;YACxC,MAAM,GAAG,GAAG,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;YAChC,GAAG,GAAG,IAAI,GAAG,WAAW,GAAG,GAAG,CAAC;SAChC,QAAQ,GAAG,IAAI,OAAO,EAAE;QACzB,OAAO,GAAG,GAAG,aAAa,CAAC;KAC5B,CAAC;CACH;AAED,SAAS,gBAAgB,CAAC,KAAa;IACrC,MAAM,aAAa,GAAG,KAAK,GAAG,CAAC,CAAC;IAChC,IAAI,2BAA2B,CAAC,aAAa,CAAC,EAAE;QAC9C,MAAM,SAAS,GAAG,CAAC,CAAC,aAAa,GAAG,WAAW,IAAI,CAAC,IAAI,CAAC,CAAC;QAC1D,IAAI,oBAAoB,CAAC,SAAS,CAAC,EAAE;YACnC,OAAO,sBAAsB,CAAC,SAAS,CAAC,CAAC;SAC1C;KACF;IACD,OAAO,yBAAyB,CAAC,aAAa,CAAC,CAAC;CACjD;AAED,SAAS,4BAA4B,CAAC,GAAW,EAAE,GAAW;IAC5D,OAAO,MAAM;QACX,IAAI,GAAG,GAAG,CAAC,CAAC;QACZ,GAAG;YACD,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;YAC/B,MAAM,GAAG,GAAG,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;YAChC,GAAG;gBACD,CAAC,IAAI,GAAG,UAAU,IAAI,WAAW;oBACjC,GAAG;qBACF,IAAI,GAAG,WAAW,GAAG,CAAC,uBAAuB,GAAG,CAAC,CAAC,CAAC;SACvD,QAAQ,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,EAAE;QACjC,OAAO,GAAG,CAAC;KACZ,CAAC;CACH;;;;;;AAOD,SAAgB,OAAO,CAAC,GAAW,EAAE,GAAW;IAC9C,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IACtB,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IACtB,IAAI,GAAG,GAAG,CAAC,uBAAuB,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;QACpD,MAAM,IAAI,UAAU,CAClB,+BAA+B,CAAC,uBAAuB,EAAE,CAC1D,CAAC;KACH;SAAM,IAAI,GAAG,GAAG,uBAAuB,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;QAC1D,MAAM,IAAI,UAAU,CAClB,8BAA8B,uBAAuB,EAAE,CACxD,CAAC;KACH;IAED,MAAM,KAAK,GAAG,GAAG,GAAG,GAAG,CAAC;IACxB,IAAI,KAAK,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE;QAClC,OAAO,MAAM,GAAG,CAAC;KAClB;SAAM,IAAI,KAAK,KAAK,UAAU,EAAE;QAC/B,IAAI,GAAG,KAAK,CAAC,EAAE;YACb,OAAO,MAAM,CAAC;SACf;aAAM;YACL,OAAO,GAAG,CAAC,KAAK,EAAE,GAAG,GAAG,UAAU,CAAC,CAAC;SACrC;KACF;SAAM,IAAI,KAAK,GAAG,UAAU,EAAE;QAC7B,OAAO,GAAG,CAAC,gBAAgB,CAAC,KAAK,CAAC,EAAE,GAAG,CAAC,CAAC;KAC1C;SAAM,IAAI,KAAK,KAAK,oBAAoB,EAAE;QACzC,OAAO,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;KACzB;SAAM,IAAI,KAAK,GAAG,oBAAoB,EAAE;QACvC,OAAO,GAAG,CAAC,gBAAgB,CAAC,KAAK,CAAC,EAAE,GAAG,CAAC,CAAC;KAC1C;SAAM,IAAI,GAAG,GAAG,CAAC,GAAG,GAAG,KAAK,oBAAoB,EAAE;QACjD,OAAO,GAAG,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;KAC7B;SAAM,IACL,GAAG,KAAK,CAAC,uBAAuB;QAChC,GAAG,KAAK,uBAAuB,EAC/B;QACA,OAAO,SAAS,CAAC;KAClB;SAAM,IAAI,GAAG,KAAK,CAAC,uBAAuB,IAAI,GAAG,KAAK,oBAAoB,EAAE;QAC3E,OAAO,KAAK,CAAC;KACd;SAAM,IAAI,GAAG,KAAK,CAAC,oBAAoB,IAAI,GAAG,KAAK,uBAAuB,EAAE;QAC3E,OAAO,GAAG,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;KACtB;SAAM,IAAI,GAAG,KAAK,uBAAuB,EAAE;QAC1C,OAAO,GAAG,CAAC,4BAA4B,CAAC,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;KAC/D;SAAM;QACL,OAAO,4BAA4B,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;KAC/C;CACF;;AC7ID,SAAS,cAAc,CAAC,MAAc;IACpC,OAAO,CAAC,MAAM,CAAC,IAAI,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC;CAClC;AAED,SAAS,QAAQ,CACf,YAA0B,EAC1B,KAAa;IAEb,OAAO,MAAM,IAAI,YAAY,CAAC,MAAM,CAAC,GAAG,KAAK,CAAC;CAC/C;AAED,SAAS,WAAW,CAAC,UAAkB;IACrC,IAAI,UAAU,IAAI,CAAC,EAAE;QACnB,OAAO,MAAM,KAAK,CAAC;KACpB;SAAM,IAAI,UAAU,IAAI,CAAC,EAAE;QAC1B,OAAO,MAAM,IAAI,CAAC;KACnB;SAAM;QACL,MAAM,MAAM,GAAG,UAAU,GAAG,WAAW,CAAC;QACxC,IAAI,MAAM,GAAG,CAAC,KAAK,CAAC,EAAE;YACpB,OAAO,QAAQ,CAAC,KAAK,EAAE,CAAC,MAAM,GAAG,UAAU,IAAI,CAAC,CAAC,CAAC;SACnD;aAAM;YACL,OAAO,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,uBAAuB,CAAC,CAAC,CAAC;SAC3E;KACF;CACF;AAuBD,SAAgB,IAAI,CAClB,SAAkB,EAClB,WAAoB;IAEpB,IAAI,WAAW,IAAI,IAAI,EAAE;QACvB,IAAI,SAAS,IAAI,IAAI,EAAE;YACrB,OAAO,cAAc,CAAC;SACvB;QACD,OAAO,WAAW,CAAC,SAAS,CAAC,CAAC;KAC/B;SAAM;QACL,IAAI,SAAU,IAAI,CAAC,EAAE;YACnB,OAAO,MAAM,KAAK,CAAC;SACpB;aAAM,IAAI,SAAU,IAAI,WAAW,EAAE;YACpC,OAAO,MAAM,IAAI,CAAC;SACnB;QACD,OAAO,QAAQ,CAAC,OAAO,CAAC,CAAC,EAAE,WAAW,GAAG,CAAC,CAAC,EAAE,SAAU,CAAC,CAAC;KAC1D;CACF;;ACnED;;;;;;AAMA,SAAgB,IAAI,CAAC,KAAW,EAAE,GAAS;IACzC,MAAM,YAAY,GAAG,OAAO,CAAC,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC;IAC3C,OAAO,MAAM,IAAI,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC;CACjD;;ACTD;;;;AAIA,SAAgB,GAAG,CAAC,SAAiB;IACnC,OAAO,OAAO,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC;CAC9B;;ACND;;;;;;AAMA,SAAgB,IAAI,CAClB,SAAiB,EACjB,QAAgB;IAEhB,MAAM,YAAY,GAAG,GAAG,CAAC,SAAS,CAAC,CAAC;IACpC,OAAO,MAAM;QACX,MAAM,MAAM,GAAG,EAAE,CAAC;QAClB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,EAAE,EAAE,CAAC,EAAE;YACjC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC;SACnC;QACD,OAAO,MAAM,CAAC;KACf,CAAC;CACH;;AClBD;;AAGA,MAAM,mBAAmB,GACvB,kEAAkE,CAAC;AAcrE,SAAgB,MAAM,CAAC,OAAe,mBAAmB;IACvD,MAAM,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC;IAC/B,IAAI,CAAC,UAAU,EAAE;QACf,MAAM,IAAI,KAAK,CAAC,yCAAyC,CAAC,CAAC;KAC5D;IAED,MAAM,YAAY,GAAG,OAAO,CAAC,CAAC,EAAE,UAAU,GAAG,CAAC,CAAC,CAAC;IAChD,OAAO,CAAC,MAAM,EAAE,MAAM;QACpB,IAAI,MAAM,GAAG,EAAE,CAAC;QAChB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,EAAE,CAAC,EAAE;YAC/B,MAAM,CAAC,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC;YAC/B,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;SAC1B;QACD,OAAO,MAAM,CAAC;KACf,CAAC;CACH;;ACjCD,MAAM,cAAc,GAAG,kBAAkB,CAAC;AAC1C,MAAM,QAAQ,GAAG,MAAM,CAAC,cAAc,CAAC,CAAC;AACxC,MAAM,QAAQ,GAAG,MAAM,CAAC,cAAc,CAAC,WAAW,EAAE,CAAC,CAAC;;;;;;;AAQtD,SAAgB,GAAG,CAAC,SAAmB;IACrC,IAAI,SAAS,EAAE;QACb,OAAO,QAAQ,CAAC;KACjB;SAAM;QACL,OAAO,QAAQ,CAAC;KACjB;CACF;;SCnBe,oBAAoB,CAAC,KAAa,EAAE,MAAc;IAChE,IAAI,KAAK,GAAG,CAAC,EAAE;QACb,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,GAAG,MAAM,EAAE,CAAC,CAAC,CAAC;KACpC;SAAM;QACL,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;KAChC;CACF;;SCNe,SAAS,CAAC,KAAa;IACrC,MAAM,GAAG,GAAG,CAAC,KAAK,CAAC;IACnB,IAAI,GAAG,GAAG,CAAC,EAAE;QACX,OAAO,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;KACvB;SAAM;QACL,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;KACxB;CACF;;ACFD;;;;;;;AAOA,SAAgB,IAAI,CAClB,MAAc,EACd,MAAoB,EACpB,KAAc,EACd,GAAY;IAEZ,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;IAC7B,IAAI,MAAM,KAAK,CAAC,EAAE;QAChB,MAAM,IAAI,UAAU,CAAC,iCAAiC,CAAC,CAAC;KACzD;IACD,MAAM,KAAK,GACT,KAAK,IAAI,IAAI,GAAG,CAAC,GAAG,oBAAoB,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,MAAM,CAAC,CAAC;IACrE,MAAM,MAAM,GACV,GAAG,KAAK,KAAK,CAAC,GAAG,MAAM,GAAG,oBAAoB,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE,MAAM,CAAC,CAAC;IACzE,IAAI,KAAK,IAAI,MAAM,EAAE;QACnB,MAAM,IAAI,UAAU,CAAC,8BAA8B,KAAK,QAAQ,MAAM,EAAE,CAAC,CAAC;KAC3E;IACD,MAAM,YAAY,GAAG,OAAO,CAAC,KAAK,EAAE,MAAM,GAAG,CAAC,CAAC,CAAC;IAChD,OAAO,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC;CACrC;;SC7Be,QAAQ,CACtB,YAA0B,EAC1B,UAAkB;IAElB,IAAI,UAAU,KAAK,CAAC,EAAE;QACpB,OAAO,YAAY,CAAC;KACrB;SAAM,IAAI,UAAU,KAAK,CAAC,EAAE;QAC3B,OAAO,MAAM,CAAC,CAAC;KAChB;SAAM;QACL,OAAO,MAAM,IAAI,YAAY,CAAC,MAAM,CAAC,GAAG,UAAU,CAAC;KACpD;CACF;;ACTD;;;AAGA,SAAgB,sBAAsB,CAAC,MAAc;IACnD,OAAO,MAAM,CAAC,MAAM,CAAC,GAAG,uBAAuB,CAAC;CACjD;;ACLD;;;AAGA,SAAgB,sBAAsB,CAAC,MAAc;IACnD,OAAO,UAAU,CAAC,MAAM,CAAC,GAAG,uBAAuB,CAAC;CACrD;;ACHD;;;;;;AAMA,SAAgB,IAAI,CAClB,GAAW,EACX,GAAW,EACX,YAAqB,KAAK;IAE1B,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;QAClB,MAAM,IAAI,UAAU,CAAC,oCAAoC,CAAC,CAAC;KAC5D;SAAM,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;QACzB,MAAM,IAAI,UAAU,CAAC,oCAAoC,CAAC,CAAC;KAC5D;IACD,OAAO,GAAG,CACR,QAAQ,CACN,SAAS,GAAG,sBAAsB,GAAG,sBAAsB,EAC3D,GAAG,GAAG,GAAG,CACV,EACD,GAAG,CACJ,CAAC;CACH;;AC7BM,MAAM,UAAU,GAAG,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC;;ACGhD;;;;;;AAMA,SAAgB,OAAO,CACrB,MAAc,EACd,KAAU,EACV,SAAiB,CAAC;IAElB,MAAM,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;IAC5B,IAAI,MAAM,EAAE;QACV,KAAK,IAAI,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,EAAE,CAAC,EAAE;YAChD,MAAM,YAAY,GAAG,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YACnC,MAAM,CAAC,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC;YAC/B,IAAI,CAAC,KAAK,CAAC,EAAE;gBACX,MAAM,GAAG,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;gBACrB,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;gBACpB,KAAK,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;aAChB;SACF;KACF;IACD,OAAO,KAAK,CAAC;CACd;;ACvBD;;;;;;;AAOA,SAAgB,MAAM,CACpB,MAAc,EACd,UAAwB,EACxB,UAAkB;IAElB,IACE,UAAU,GAAG,CAAC;QACd,UAAU,GAAG,UAAU,CAAC,MAAM;QAC9B,CAAC,QAAQ,CAAC,UAAU,CAAC,EACrB;QACA,MAAM,IAAI,UAAU,CAClB,qEAAqE,CACtE,CAAC;KACH;IAED,IAAI,UAAU,KAAK,CAAC,EAAE;QACpB,OAAO,EAAE,CAAC;KACX;IAED,MAAM,KAAK,GAAG,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;IAC1C,MAAM,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;IAC5B,IAAI,MAAM,KAAK,UAAU,EAAE;QACzB,OAAO,OAAO,CAAC,MAAM,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC;KAClC;IACD,MAAM,UAAU,GAAG,MAAM,GAAG,UAAU,CAAC;IACvC,OAAO,OAAO,CAAC,MAAM,EAAE,KAAK,EAAE,UAAU,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;CACjE;;ACrCM,MAAM,YAAY,GAAG,CAAC;IAC3B,IAAI;QACF,IAAK,GAAW,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,KAAK,EAAE;YACpC,OAAO,CAAC,OAAe,EAAE,KAAa,KACnC,OAAe,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;SAClC;KACF;IAAC,OAAO,CAAC,EAAE;;KAEX;IACD,OAAO,CAAC,OAAe,EAAE,KAAa;QACpC,IAAI,MAAM,GAAG,EAAE,CAAC;QAChB,OAAO,KAAK,GAAG,CAAC,EAAE;YAChB,IAAI,KAAK,GAAG,CAAC,EAAE;gBACb,MAAM,IAAI,OAAO,CAAC;aACnB;YACD,KAAK,KAAK,CAAC,CAAC;YACZ,OAAO,IAAI,OAAO,CAAC;SACpB;QACD,OAAO,MAAM,CAAC;KACf,CAAC;CACH,GAAG,CAAC;;ACjBL,SAAS,OAAO,CAAC,IAAY,EAAE,SAAiB;IAC9C,OAAO,YAAY,CAAC,GAAG,EAAE,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC;CAC1D;;;;;;AAOD,SAAgB,KAAK,CAAC,MAAc;IAClC,MAAM,CAAC,GAAG,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IAC9B,MAAM,CAAC,GAAG,MAAM,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;IAC5B,MAAM,CAAC,GAAG,MAAM,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;IAC5B,MAAM,CAAC,GAAG,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IAE9B,QACE,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAC1B,GAAG;QACH,OAAO,CAAC,CAAC,CAAC,GAAG,MAAM,EAAE,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACrC,GAAG;QACH,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,MAAM,IAAI,MAAM,EAAE,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACvD,GAAG;QACH,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,MAAM,IAAI,MAAM,EAAE,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAChD,GAAG;QACH,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,MAAM,EAAE,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAC5C,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAC1B;CACH;;AC3BD;;;AAGA,MAAa,UAAU,GAAW;IAChC,IAAI;QACF,OAAO,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,WAAW,IAAI,CAAC,CAAC;KAC1C;CACF;;ACaD;;;;;AAMA,MAAa,MAAM;;;;;IAOjB,YAAY,SAAiB,UAAU;QACrC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;KACtB;;;;IAKM,KAAK;QACV,OAAO,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;KAC3B;;;;IAKM,MAAM;QACX,OAAO,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;KAC5B;;;;IAKM,MAAM;QACX,OAAO,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;KAC5B;;;;IAKM,UAAU;QACf,OAAO,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;KAChC;;;;IAKM,KAAK;QACV,OAAO,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;KAC3B;;;;IAKM,SAAS;QACd,OAAO,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;KAC/B;;;;;;IAOM,OAAO,CAAC,GAAW,EAAE,GAAW;QACrC,OAAO,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;KACvC;;;;IAKM,sBAAsB;QAC3B,OAAO,sBAAsB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;KAC5C;;;;IAKM,sBAAsB;QAC3B,OAAO,sBAAsB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;KAC5C;;;;;;;IAQM,IAAI,CAAC,GAAW,EAAE,GAAW,EAAE,YAAqB,KAAK;QAC9D,OAAO,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,SAAS,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;KAC/C;IAiBM,IAAI,CAAC,SAAkB,EAAE,WAAoB;QAClD,OAAO,IAAI,CAAC,SAAU,EAAE,WAAY,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;KACpD;;;;;;;;IASM,IAAI,CAAI,MAAoB,EAAE,KAAc,EAAE,GAAY;QAC/D,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC;KAC9C;;;;;IAMM,OAAO,CAAI,KAAU;QAC1B,OAAO,OAAO,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;KACpC;;;;;;;IAQM,MAAM,CAAI,UAAwB,EAAE,UAAkB;QAC3D,OAAO,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC;KACpD;;;;;IAMM,GAAG,CAAC,SAAiB;QAC1B,OAAO,GAAG,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;KACpC;;;;;;IAOM,IAAI,CAAC,SAAiB,EAAE,QAAgB;QAC7C,OAAO,IAAI,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;KAC/C;;;;;;IAOM,KAAK;QACV,OAAO,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;KAC3B;IAcM,MAAM,CAAC,MAAc,EAAE,IAAa;QACzC,OAAO,MAAM,CAAC,IAAK,CAAC,CAAC,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;KAC3C;;;;;;;IAQM,GAAG,CAAC,MAAc,EAAE,SAAmB;QAC5C,OAAO,GAAG,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;KAC5C;;;;;;IAOM,IAAI,CAAC,KAAW,EAAE,GAAS;QAChC,OAAO,IAAI,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;KACtC;CACF;;AC7ND;;;AAGA,MAAM,QAAQ,GAAsB,CAAC;IACnC,IAAI;QACF,MAAM,MAAM,GAAG,IAAI,WAAW,CAAC,CAAC,CAAC,CAAC;QAClC,MAAM,IAAI,GAAG,IAAI,UAAU,CAAC,MAAM,CAAC,CAAC;QACpC,IAAI,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC;QACrB,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,UAAU,EAAE;YAC3B,OAAO,UAAU,CAAC;SACnB;KACF;IAAC,OAAO,CAAC,EAAE;;KAEX;IACD,OAAQ,KAAsC,CAAC;CAChD,GAAG,CAAC;;ACdL,IAAI,IAAI,GAAsB,IAAI,CAAC;AACnC,MAAM,KAAK,GAAG,GAAG,CAAC;AAClB,IAAI,KAAK,GAAG,KAAK,CAAC;;;;;;;;;;AAWlB,MAAa,aAAa,GAAW;IACnC,IAAI;QACF,IAAI,KAAK,IAAI,KAAK,EAAE;YAClB,IAAI,IAAI,KAAK,IAAI,EAAE;gBACjB,IAAI,GAAG,IAAIA,QAAU,CAAC,KAAK,CAAC,CAAC;aAC9B;YACD,MAAM,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;YAC7B,KAAK,GAAG,CAAC,CAAC;SACX;QACD,OAAO,IAAK,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC;KAC3B;CACF;;ACxBD;;;;;;;AAOA,SAAgB,aAAa,CAC3B,SAAiB,UAAU,EAC3B,SAAiB,EAAE;IAEnB,MAAM,KAAK,GAAa,EAAE,CAAC;IAC3B,KAAK,CAAC,IAAI,CAAC,IAAI,IAAI,EAAE,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC;IACrC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,EAAE,CAAC,EAAE;QAC/B,KAAK,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;KAC9B;IACD,OAAO,KAAK,CAAC;CACd;;AClBD;;;AAGA,AAAO,MAAM,IAAI,GAAqC,CAAC;IACrD,IAAI;QACF,IAAK,IAAY,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE;YAC5C,OAAQ,IAAY,CAAC,IAAI,CAAC;SAC3B;KACF;IAAC,OAAO,CAAC,EAAE;;KAEX;IACD,MAAM,UAAU,GAAG,MAAM,CAAC;IAC1B,OAAO,CAAC,CAAS,EAAE,CAAS;QAC1B,MAAM,EAAE,GAAG,CAAC,CAAC,KAAK,EAAE,IAAI,UAAU,CAAC;QACnC,MAAM,EAAE,GAAG,CAAC,GAAG,UAAU,CAAC;QAC1B,MAAM,EAAE,GAAG,CAAC,CAAC,KAAK,EAAE,IAAI,UAAU,CAAC;QACnC,MAAM,EAAE,GAAG,CAAC,GAAG,UAAU,CAAC;;;QAG1B,OAAO,CAAC,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC;KAC5D,CAAC;CACH,GAAG,CAAC;;ACjBL,MAAM,UAAU,GAAG,GAAG,CAAC;AACvB,MAAM,SAAS,GAAG,UAAU,GAAG,CAAC,CAAC;AACjC,MAAM,CAAC,GAAG,GAAG,CAAC;AACd,MAAM,kBAAkB,GAAG,UAAU,GAAG,CAAC,CAAC;AAC1C,MAAM,CAAC,GAAG,UAAU,CAAC;;;;;;;AAQrB,MAAa,oBAAoB;;;;;IAiC/B;QARiB,SAAI,GAAG,IAAIA,QAAU,CAAC,UAAU,CAAC,CAAC;QAC3C,UAAK,GAAG,CAAC,CAAC;QACV,SAAI,GAAG,CAAC,CAAC;KAMO;;;;;IA5BjB,OAAO,IAAI,CAAC,OAAe;QAChC,OAAO,IAAI,oBAAoB,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;KACjD;;;;;IAMM,OAAO,aAAa,CAAC,MAAyB;QACnD,OAAO,IAAI,oBAAoB,EAAE,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;KACzD;;;;;IAMM,OAAO,QAAQ;QACpB,OAAO,oBAAoB,CAAC,aAAa,CAAC,aAAa,EAAE,CAAC,CAAC;KAC5D;;;;IAeM,IAAI;QACT,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC,KAAK,UAAU,EAAE;YAClC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACvB,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;SAChB;QAED,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACpC,IAAI,CAAC,KAAK,GAAG,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC,IAAI,CAAC,CAAC;QAClC,IAAI,CAAC,IAAI,IAAI,CAAC,CAAC;QACf,OAAO,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;KAC1B;;;;;;;IAQM,WAAW;QAChB,OAAO,IAAI,CAAC,IAAI,CAAC;KAClB;;;;;IAMM,OAAO,CAAC,KAAa;QAC1B,IAAI,KAAK,IAAI,CAAC,EAAE;YACd,OAAO,IAAI,CAAC;SACb;QACD,IAAI,CAAC,IAAI,IAAI,KAAK,CAAC;QACnB,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC,KAAK,UAAU,EAAE;YAClC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACvB,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;SAChB;QACD,OAAO,KAAK,GAAG,IAAI,CAAC,KAAK,GAAG,UAAU,EAAE;YACtC,KAAK,IAAI,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC;YACjC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACvB,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;SAChB;QACD,IAAI,CAAC,KAAK,GAAG,CAAC,IAAI,CAAC,KAAK,GAAG,KAAK,IAAI,CAAC,CAAC;QACtC,OAAO,IAAI,CAAC;KACb;IAEO,IAAI,CAAC,OAAe;QAC1B,IAAI,QAAQ,GAAG,CAAC,CAAC;QACjB,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,QAAQ,GAAG,OAAO,GAAG,CAAC,CAAC;QAEtC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;YAC/C,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,QAAQ;gBACrB,CAAC,IAAI,CAAC,QAAQ,IAAI,QAAQ,KAAK,EAAE,CAAC,EAAE,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;SAC5D;QACD,IAAI,CAAC,KAAK,GAAG,UAAU,CAAC;QACxB,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC;QACd,OAAO,IAAI,CAAC;KACb;IAEO,aAAa,CAAC,MAAyB;QAC7C,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QACtB,aAAa,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QACjC,OAAO,IAAI,CAAC;KACb;CACF;AAED,SAAS,WAAW,CAAC,IAAgB;IACnC,IAAI,CAAC,GAAG,CAAC,CAAC;IACV,IAAI,GAAG,GAAG,CAAC,CAAC;IACZ,OAAO,CAAC,CAAC,GAAG,CAAC,IAAI,kBAAkB,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;QACpD,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,UAAU,KAAK,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC;QAC/D,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,IAAI,GAAG,KAAK,CAAC,CAAC,IAAI,GAAG,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;KACjE;IAED,OAAO,CAAC,CAAC,GAAG,CAAC,IAAI,SAAS,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;QAC3C,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,UAAU,KAAK,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC;QAC/D,IAAI,CAAC,CAAC,CAAC;YACL,IAAI,CAAC,CAAC,CAAC,GAAG,kBAAkB,IAAI,CAAC,CAAC,IAAI,GAAG,KAAK,CAAC,CAAC,IAAI,GAAG,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;KAC1E;IAED,GAAG,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,UAAU,KAAK,IAAI,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC;IAC7D,IAAI,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,GAAG,KAAK,CAAC,CAAC,IAAI,GAAG,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;CACnE;AAED,SAAS,MAAM,CAAC,KAAa;IAC3B,KAAK,IAAI,KAAK,KAAK,EAAE,CAAC;IACtB,KAAK,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI,UAAU,CAAC;IACnC,KAAK,IAAI,CAAC,KAAK,IAAI,EAAE,IAAI,UAAU,CAAC;IACpC,OAAO,KAAK,IAAI,KAAK,KAAK,EAAE,CAAC,CAAC;CAC/B;AAED,SAAS,aAAa,CAAC,IAAgB,EAAE,MAAyB;IAChE,IAAI,CAAC,GAAG,CAAC,CAAC;IACV,IAAI,CAAC,GAAG,CAAC,CAAC;IACV,MAAM,YAAY,GAAG,MAAM,CAAC,MAAM,CAAC;IACnC,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE,UAAU,CAAC,GAAG,CAAC,CAAC;IAC/C,IAAI,QAAQ,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IAC3B,OAAO,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,EAAE;QACvB,IAAI,CAAC,CAAC,CAAC,GAAG,QAAQ;YAChB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,QAAQ,IAAI,QAAQ,KAAK,EAAE,CAAC,EAAE,UAAU,CAAC;iBACvD,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;iBACd,CAAC,GAAG,CAAC,CAAC;gBACT,CAAC,CAAC;QACJ,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAChB,EAAE,CAAC,CAAC;QACJ,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,SAAS,EAAE;YACvB,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC;YAC1B,CAAC,GAAG,CAAC,CAAC;SACP;QACD,IAAI,CAAC,IAAI,YAAY,EAAE;YACrB,CAAC,GAAG,CAAC,CAAC;SACP;KACF;IACD,KAAK,CAAC,GAAG,SAAS,EAAE,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,EAAE;QACpC,IAAI,CAAC,CAAC,CAAC,GAAG,QAAQ;YAChB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,QAAQ,IAAI,QAAQ,KAAK,EAAE,CAAC,EAAE,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACvE,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAChB,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,SAAS,EAAE;YACvB,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC;YAC1B,CAAC,GAAG,CAAC,CAAC;SACP;KACF;IACD,IAAI,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC;CACtB;;AC/KD,IAAIC,MAAI,GAAsB,IAAI,CAAC;AACnC,MAAMC,OAAK,GAAG,GAAG,CAAC;AAClB,IAAIC,OAAK,GAAGD,OAAK,CAAC;;;;;;;;;;AAWlB,MAAa,UAAU,GAAW;IAChC,IAAI;QACF,IAAIC,OAAK,IAAID,OAAK,EAAE;YAClBD,MAAI,GAAG,IAAI,UAAU,CACnB,IAAI,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,WAAW,CAAC,CAAC,GAAGC,OAAK,CAAC,CAAC,CAAC,MAAM,CAC/D,CAAC;YACFC,OAAK,GAAG,CAAC,CAAC;SACX;QACD,OAAOF,MAAK,CAACE,OAAK,EAAE,CAAC,GAAG,CAAC,CAAC;KAC3B;CACF;;ACrBD;;;;;;;AAOA,SAAgB,MAAM,CACpB,MAAoB,EACpB,KAAc,EACd,GAAY;IAEZ,MAAM,KAAK,GAAG,UAAU,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC;IAClD,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;QACtB,MAAM,IAAI,UAAU,CAAC,yCAAyC,CAAC,CAAC;KACjE;IACD,MAAM,YAAY,GAAG,OAAO,CAAC,CAAC,EAAE,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IAClD,OAAO,MAAM,IAAI,KAAK,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC;CAC9C;;;;"}