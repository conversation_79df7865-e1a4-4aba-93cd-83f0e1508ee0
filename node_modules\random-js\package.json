{"name": "random-js", "description": "A mathematically correct random number generator library for JavaScript.", "version": "2.1.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "keywords": ["random"], "homepage": "https://github.com/ckknight/random-js", "bugs": "https://github.com/ckknight/random-js/issues", "license": "MIT", "repository": "git://github.com/ckknight/random-js", "main": "dist/random-js.umd.js", "module": "dist/random-js.esm.js", "unpkg": "dist/random-js.umd.min.js", "types": "dist/index.d.ts", "files": ["dist"], "readmeFilename": "README.md", "devDependencies": {"@types/jest": "^24.0.5", "@types/node": "^11.9.4", "benchmark": "^2.1.4", "jest": "^24.1.0", "prettier": "^1.16.4", "rimraf": "^2.6.3", "rollup": "^1.2.1", "rollup-plugin-typescript": "^1.0.0", "rollup-plugin-typescript2": "^0.19.2", "terser": "^3.16.1", "ts-jest": "^23.10.5", "tslint": "^5.12.1", "tslint-config-prettier": "^1.18.0", "typescript": "^3.3.3"}, "scripts": {"clean": "<PERSON><PERSON><PERSON> dist", "build:umd": "rollup -c rollup.config.es3.js --format umd -o dist/random-js.umd.js --name Random -m", "build:esm": "rollup -c rollup.config.js --format esm --sourcemap true -o dist/random-js.esm.js", "prebuild": "yarn clean", "build": "yarn build:umd && yarn build:esm", "minify": "terser --source-map content=dist/random-js.umd.js.map --compress --mangle --output dist/random-js.umd.min.js dist/random-js.umd.js", "postbuild": "yarn minify", "benchmark": "for k in benchmark/*.js; do node $k; done", "test": "jest", "lint": "tslint --project .", "prepublish": "yarn clean && yarn lint && yarn test && yarn build"}, "testling": {"files": "spec/*.js"}, "sideEffects": false}