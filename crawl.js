import { configDotenv } from 'dotenv';
import { google } from 'googleapis';
import { Random } from "random-js";


const random = new Random(); // uses the nativeMath engine
configDotenv();

const books = google.books({
    auth: process.env.GOOGLE_BOOKS_API_KEY,
    version: 'v1',
});

const qs = [
    'harry potter',
    'the lord of the rings',
    'the two towers',
    'the return of the king',
    'the silmarillion',
    'the hobbit',
    'the fellowship of the ring',
    'spring boot',
    'java',
    'javascript',
]

for (const q of qs) {
    const results = await books.volumes.list({
        q,
        maxResults: 1,
    });
    const mongoModel = results.data.items.map((item) => {
        return {
            title: item.volumeInfo.title,
            author: item.volumeInfo.authors?.[0],
            description: item.volumeInfo.description,
            originalPrice: item.saleInfo.listPrice?.amount || random.integer(10, 1000),
            discountRate: item.saleInfo.retailPrice?.amount || random.integer(0, 50),
            price: item.saleInfo.retailPrice?.amount || random.integer(10, 1000),
            stock: random.integer(0, 100),
            isbn: item.volumeInfo.industryIdentifiers?.[0].identifier,
            publisher: item.volumeInfo.publisher,
            publicationYear: item.volumeInfo.publishedDate,
            genres: item.volumeInfo.categories,
            coverImage: item.volumeInfo.imageLinks?.thumbnail,
        };
    });

    const flattenedArray = mongoModel.map((item) => {
        return {
            ...item,
            genres: item.genres?.join(', '),
        };
    });

    console.log(mongoModel);
}
