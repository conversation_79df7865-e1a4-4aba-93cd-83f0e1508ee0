{"version": 3, "file": "random-js.umd.js", "sources": ["../src/utils/constants.ts", "../src/distribution/int32.ts", "../src/utils/add.ts", "../src/distribution/int53.ts", "../src/distribution/int53Full.ts", "../src/distribution/uint32.ts", "../src/distribution/uint53.ts", "../src/distribution/uint53Full.ts", "../src/distribution/integer.ts", "../src/distribution/bool.ts", "../src/distribution/date.ts", "../src/distribution/die.ts", "../src/distribution/dice.ts", "../src/distribution/string.ts", "../src/distribution/hex.ts", "../src/utils/convertSliceArgument.ts", "../src/utils/toInteger.ts", "../src/distribution/pick.ts", "../src/utils/multiply.ts", "../src/distribution/realZeroToOneExclusive.ts", "../src/distribution/realZeroToOneInclusive.ts", "../src/distribution/real.ts", "../src/utils/sliceArray.ts", "../src/distribution/shuffle.ts", "../src/distribution/sample.ts", "../src/utils/stringRepeat.ts", "../src/distribution/uuid4.ts", "../src/engine/nativeMath.ts", "../src/Random.ts", "../src/utils/Int32Array.ts", "../src/engine/browserCrypto.ts", "../src/utils/createEntropy.ts", "../src/utils/imul.ts", "../src/engine/MersenneTwister19937.ts", "../src/engine/nodeCrypto.ts", "../src/distribution/picker.ts"], "sourcesContent": ["export const SMALLEST_UNSAFE_INTEGER = 0x20000000000000;\nexport const LARGEST_SAFE_INTEGER = SMALLEST_UNSAFE_INTEGER - 1;\nexport const UINT32_MAX = -1 >>> 0;\nexport const UINT32_SIZE = UINT32_MAX + 1;\nexport const INT32_SIZE = UINT32_SIZE / 2;\nexport const INT32_MAX = INT32_SIZE - 1;\nexport const UINT21_SIZE = 1 << 21;\nexport const UINT21_MAX = UINT21_SIZE - 1;\n", "import { Engine } from \"../types\";\n\n/**\n * Returns a value within [-0x80000000, 0x7fffffff]\n */\nexport function int32(engine: Engine): number {\n  return engine.next() | 0;\n}\n", "import { Distribution } from \"../types\";\n\nexport function add(distribution: Distribution, addend: number): Distribution {\n  if (addend === 0) {\n    return distribution;\n  } else {\n    return engine => distribution(engine) + addend;\n  }\n}\n", "import { Engine } from \"../types\";\nimport {\n  SMALLEST_UNSAFE_INTEGER,\n  UINT21_MAX,\n  UINT21_SIZE,\n  UINT32_SIZE\n} from \"../utils/constants\";\n\n/**\n * Returns a value within [-0x20000000000000, 0x1fffffffffffff]\n */\nexport function int53(engine: Engine): number {\n  const high = engine.next() | 0;\n  const low = engine.next() >>> 0;\n  return (\n    (high & UINT21_MAX) * UINT32_SIZE +\n    low +\n    (high & UINT21_SIZE ? -SMALLEST_UNSAFE_INTEGER : 0)\n  );\n}\n", "import { Engine } from \"../types\";\nimport {\n  SMALLEST_UNSAFE_INTEGER,\n  UINT21_MAX,\n  UINT21_SIZE,\n  UINT32_SIZE\n} from \"../utils/constants\";\n\n/**\n * Returns a value within [-0x20000000000000, 0x20000000000000]\n */\nexport function int53Full(engine: Engine): number {\n  while (true) {\n    const high = engine.next() | 0;\n    if (high & 0x400000) {\n      if ((high & 0x7fffff) === 0x400000 && (engine.next() | 0) === 0) {\n        return SMALLEST_UNSAFE_INTEGER;\n      }\n    } else {\n      const low = engine.next() >>> 0;\n      return (\n        (high & UINT21_MAX) * UINT32_SIZE +\n        low +\n        (high & UINT21_SIZE ? -SMALLEST_UNSAFE_INTEGER : 0)\n      );\n    }\n  }\n}\n", "import { Engine } from \"../types\";\n\n/**\n * Returns a value within [0, 0xffffffff]\n */\nexport function uint32(engine: Engine): number {\n  return engine.next() >>> 0;\n}\n", "import { Engine } from \"../types\";\nimport { UINT21_MAX, UINT32_SIZE } from \"../utils/constants\";\n\n/**\n * Returns a value within [0, 0x1fffffffffffff]\n */\nexport function uint53(engine: Engine): number {\n  const high = engine.next() & UINT21_MAX;\n  const low = engine.next() >>> 0;\n  return high * UINT32_SIZE + low;\n}\n", "import { Engine } from \"../types\";\nimport {\n  SMALLEST_UNSAFE_INTEGER,\n  UINT21_MAX,\n  UINT21_SIZE,\n  UINT32_SIZE\n} from \"../utils/constants\";\n\n/**\n * Returns a value within [0, 0x20000000000000]\n */\nexport function uint53Full(engine: Engine): number {\n  while (true) {\n    const high = engine.next() | 0;\n    if (high & UINT21_SIZE) {\n      if ((high & UINT21_MAX) === 0 && (engine.next() | 0) === 0) {\n        return SMALLEST_UNSAFE_INTEGER;\n      }\n    } else {\n      const low = engine.next() >>> 0;\n      return (high & UINT21_MAX) * UINT32_SIZE + low;\n    }\n  }\n}\n", "import { Distribution, Engine } from \"../types\";\nimport { add } from \"../utils/add\";\nimport {\n  INT32_SIZE,\n  LARGEST_SAFE_INTEGER,\n  SMALLEST_UNSAFE_INTEGER,\n  UINT21_MAX,\n  UINT21_SIZE,\n  UINT32_MAX,\n  UINT32_SIZE\n} from \"../utils/constants\";\nimport { int32 } from \"./int32\";\nimport { int53 } from \"./int53\";\nimport { int53Full } from \"./int53Full\";\nimport { uint32 } from \"./uint32\";\nimport { uint53 } from \"./uint53\";\nimport { uint53Full } from \"./uint53Full\";\n\nfunction isPowerOfTwoMinusOne(value: number): boolean {\n  return ((value + 1) & value) === 0;\n}\n\nfunction bitmask(masking: number): Distribution {\n  return (engine: Engine) => engine.next() & masking;\n}\n\nfunction downscaleToLoopCheckedRange(range: number): Distribution {\n  const extendedRange = range + 1;\n  const maximum = extendedRange * Math.floor(UINT32_SIZE / extendedRange);\n  return engine => {\n    let value = 0;\n    do {\n      value = engine.next() >>> 0;\n    } while (value >= maximum);\n    return value % extendedRange;\n  };\n}\n\nfunction downscaleToRange(range: number): Distribution {\n  if (isPowerOfTwoMinusOne(range)) {\n    return bitmask(range);\n  } else {\n    return downscaleToLoopCheckedRange(range);\n  }\n}\n\nfunction isEvenlyDivisibleByMaxInt32(value: number): boolean {\n  return (value | 0) === 0;\n}\n\nfunction upscaleWithHighMasking(masking: number): Distribution {\n  return engine => {\n    const high = engine.next() & masking;\n    const low = engine.next() >>> 0;\n    return high * UINT32_SIZE + low;\n  };\n}\n\nfunction upscaleToLoopCheckedRange(extendedRange: number): Distribution {\n  const maximum =\n    extendedRange * Math.floor(SMALLEST_UNSAFE_INTEGER / extendedRange);\n  return engine => {\n    let ret = 0;\n    do {\n      const high = engine.next() & UINT21_MAX;\n      const low = engine.next() >>> 0;\n      ret = high * UINT32_SIZE + low;\n    } while (ret >= maximum);\n    return ret % extendedRange;\n  };\n}\n\nfunction upscaleWithinU53(range: number): Distribution {\n  const extendedRange = range + 1;\n  if (isEvenlyDivisibleByMaxInt32(extendedRange)) {\n    const highRange = ((extendedRange / UINT32_SIZE) | 0) - 1;\n    if (isPowerOfTwoMinusOne(highRange)) {\n      return upscaleWithHighMasking(highRange);\n    }\n  }\n  return upscaleToLoopCheckedRange(extendedRange);\n}\n\nfunction upscaleWithinI53AndLoopCheck(min: number, max: number): Distribution {\n  return engine => {\n    let ret = 0;\n    do {\n      const high = engine.next() | 0;\n      const low = engine.next() >>> 0;\n      ret =\n        (high & UINT21_MAX) * UINT32_SIZE +\n        low +\n        (high & UINT21_SIZE ? -SMALLEST_UNSAFE_INTEGER : 0);\n    } while (ret < min || ret > max);\n    return ret;\n  };\n}\n\n/**\n * Returns a Distribution to return a value within [min, max]\n * @param min The minimum integer value, inclusive. No less than -0x20000000000000.\n * @param max The maximum integer value, inclusive. No greater than 0x20000000000000.\n */\nexport function integer(min: number, max: number): Distribution {\n  min = Math.floor(min);\n  max = Math.floor(max);\n  if (min < -SMALLEST_UNSAFE_INTEGER || !isFinite(min)) {\n    throw new RangeError(\n      `Expected min to be at least ${-SMALLEST_UNSAFE_INTEGER}`\n    );\n  } else if (max > SMALLEST_UNSAFE_INTEGER || !isFinite(max)) {\n    throw new RangeError(\n      `Expected max to be at most ${SMALLEST_UNSAFE_INTEGER}`\n    );\n  }\n\n  const range = max - min;\n  if (range <= 0 || !isFinite(range)) {\n    return () => min;\n  } else if (range === UINT32_MAX) {\n    if (min === 0) {\n      return uint32;\n    } else {\n      return add(int32, min + INT32_SIZE);\n    }\n  } else if (range < UINT32_MAX) {\n    return add(downscaleToRange(range), min);\n  } else if (range === LARGEST_SAFE_INTEGER) {\n    return add(uint53, min);\n  } else if (range < LARGEST_SAFE_INTEGER) {\n    return add(upscaleWithinU53(range), min);\n  } else if (max - 1 - min === LARGEST_SAFE_INTEGER) {\n    return add(uint53Full, min);\n  } else if (\n    min === -SMALLEST_UNSAFE_INTEGER &&\n    max === SMALLEST_UNSAFE_INTEGER\n  ) {\n    return int53Full;\n  } else if (min === -SMALLEST_UNSAFE_INTEGER && max === LARGEST_SAFE_INTEGER) {\n    return int53;\n  } else if (min === -LARGEST_SAFE_INTEGER && max === SMALLEST_UNSAFE_INTEGER) {\n    return add(int53, 1);\n  } else if (max === SMALLEST_UNSAFE_INTEGER) {\n    return add(upscaleWithinI53AndLoopCheck(min - 1, max - 1), 1);\n  } else {\n    return upscaleWithinI53AndLoopCheck(min, max);\n  }\n}\n", "import { Distribution, Engine } from \"../types\";\nimport { INT32_SIZE, SMALLEST_UNSAFE_INTEGER, UINT32_SIZE } from \"../utils/constants\";\nimport { int32 } from \"./int32\";\nimport { integer } from \"./integer\";\nimport { uint53 } from \"./uint53\";\n\nfunction isLeastBitTrue(engine: Engine) {\n  return (engine.next() & 1) === 1;\n}\n\nfunction lessThan(\n  distribution: Distribution,\n  value: number\n): Distribution<boolean> {\n  return engine => distribution(engine) < value;\n}\n\nfunction probability(percentage: number) {\n  if (percentage <= 0) {\n    return () => false;\n  } else if (percentage >= 1) {\n    return () => true;\n  } else {\n    const scaled = percentage * UINT32_SIZE;\n    if (scaled % 1 === 0) {\n      return lessThan(int32, (scaled - INT32_SIZE) | 0);\n    } else {\n      return lessThan(uint53, Math.round(percentage * SMALLEST_UNSAFE_INTEGER));\n    }\n  }\n}\n\n// tslint:disable:unified-signatures\n\n/**\n * Returns a boolean Distribution with 50% probability of being true or false\n */\nexport function bool(): Distribution<boolean>;\n/**\n * Returns a boolean Distribution with the provided `percentage` of being true\n * @param percentage A number within [0, 1] of how often the result should be `true`\n */\nexport function bool(percentage: number): Distribution<boolean>;\n/**\n * Returns a boolean Distribution with a probability of\n * `numerator` divided by `denominator` of being true\n * @param numerator The numerator of the probability\n * @param denominator The denominator of the probability\n */\nexport function bool(\n  numerator: number,\n  denominator: number\n): Distribution<boolean>;\nexport function bool(\n  numerator?: number,\n  denominator?: number\n): Distribution<boolean> {\n  if (denominator == null) {\n    if (numerator == null) {\n      return isLeastBitTrue;\n    }\n    return probability(numerator);\n  } else {\n    if (numerator! <= 0) {\n      return () => false;\n    } else if (numerator! >= denominator) {\n      return () => true;\n    }\n    return lessThan(integer(0, denominator - 1), numerator!);\n  }\n}\n", "import { Distribution } from \"../types\";\nimport { integer } from \"./integer\";\n\n/**\n * Returns a Distribution that returns a random `Date` within the inclusive\n * range of [`start`, `end`].\n * @param start The minimum `Date`\n * @param end The maximum `Date`\n */\nexport function date(start: Date, end: Date): Distribution<Date> {\n  const distribution = integer(+start, +end);\n  return engine => new Date(distribution(engine));\n}\n", "import { Distribution } from \"../types\";\nimport { integer } from \"./integer\";\n\n/**\n * Returns a Distribution to return a value within [1, sideCount]\n * @param sideCount The number of sides of the die\n */\nexport function die(sideCount: number): Distribution<number> {\n  return integer(1, sideCount);\n}\n", "import { Distribution } from \"../types\";\nimport { die } from \"./die\";\n\n/**\n * Returns a distribution that returns an array of length `dieCount` of values\n * within [1, `sideCount`]\n * @param sideCount The number of sides of each die\n * @param dieCount The number of dice\n */\nexport function dice(\n  sideCount: number,\n  dieCount: number\n): Distribution<number[]> {\n  const distribution = die(sideCount);\n  return engine => {\n    const result = [];\n    for (let i = 0; i < dieCount; ++i) {\n      result.push(distribution(engine));\n    }\n    return result;\n  };\n}\n", "import { StringDistribution } from \"../types\";\nimport { integer } from \"./integer\";\n\n// tslint:disable:unified-signatures\n\n// has 2**x chars, for faster uniform distribution\nconst DEFAULT_STRING_POOL =\n  \"abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789_-\";\n\n/**\n * Returns a distribution that returns a random string using numbers,\n * uppercase and lowercase letters, `_`, and `-` of length `length`.\n * @param length Length of the result string\n */\nexport function string(): StringDistribution;\n/**\n * Returns a distribution that returns a random string using the provided\n * string pool as the possible characters to choose from of length `length`.\n * @param length Length of the result string\n */\nexport function string(pool: string): StringDistribution;\nexport function string(pool: string = DEFAULT_STRING_POOL): StringDistribution {\n  const poolLength = pool.length;\n  if (!poolLength) {\n    throw new Error(\"Expected pool not to be an empty string\");\n  }\n\n  const distribution = integer(0, poolLength - 1);\n  return (engine, length) => {\n    let result = \"\";\n    for (let i = 0; i < length; ++i) {\n      const j = distribution(engine);\n      result += pool.charAt(j);\n    }\n    return result;\n  };\n}\n", "import { StringDistribution } from \"../types\";\nimport { string } from \"./string\";\n\nconst LOWER_HEX_POOL = \"0123456789abcdef\";\nconst lowerHex = string(LOWER_HEX_POOL);\nconst upperHex = string(LOWER_HEX_POOL.toUpperCase());\n\n/**\n * Returns a Distribution that returns a random string comprised of numbers\n * or the characters `abcdef` (or `ABCDEF`) of length `length`.\n * @param length Length of the result string\n * @param uppercase Whether the string should use `ABCDEF` instead of `abcdef`\n */\nexport function hex(uppercase?: boolean): StringDistribution {\n  if (uppercase) {\n    return upperHex;\n  } else {\n    return lowerHex;\n  }\n}\n", "export function convertSliceArgument(value: number, length: number): number {\n  if (value < 0) {\n    return Math.max(value + length, 0);\n  } else {\n    return Math.min(value, length);\n  }\n}\n", "export function toInteger(value: number) {\n  const num = +value;\n  if (num < 0) {\n    return Math.ceil(num);\n  } else {\n    return Math.floor(num);\n  }\n}\n", "import { Engine } from \"../types\";\nimport { convertSliceArgument } from \"../utils/convertSliceArgument\";\nimport { toInteger } from \"../utils/toInteger\";\nimport { integer } from \"./integer\";\n\n/**\n * Returns a random value within the provided `source` within the sliced\n * bounds of `begin` and `end`.\n * @param source an array of items to pick from\n * @param begin the beginning slice index (defaults to `0`)\n * @param end the ending slice index (defaults to `source.length`)\n */\nexport function pick<T>(\n  engine: Engine,\n  source: ArrayLike<T>,\n  begin?: number,\n  end?: number\n): T {\n  const length = source.length;\n  if (length === 0) {\n    throw new RangeError(\"Cannot pick from an empty array\");\n  }\n  const start =\n    begin == null ? 0 : convertSliceArgument(toInteger(begin), length);\n  const finish =\n    end === void 0 ? length : convertSliceArgument(toInteger(end), length);\n  if (start >= finish) {\n    throw new RangeError(`Cannot pick between bounds ${start} and ${finish}`);\n  }\n  const distribution = integer(start, finish - 1);\n  return source[distribution(engine)];\n}\n", "import { Distribution } from \"../types\";\n\nexport function multiply(\n  distribution: Distribution,\n  multiplier: number\n): Distribution {\n  if (multiplier === 1) {\n    return distribution;\n  } else if (multiplier === 0) {\n    return () => 0;\n  } else {\n    return engine => distribution(engine) * multiplier;\n  }\n}\n", "import { Engine } from \"../types\";\nimport { SMALLEST_UNSAFE_INTEGER } from \"../utils/constants\";\nimport { uint53 } from \"./uint53\";\n\n/**\n * Returns a floating-point value within [0.0, 1.0)\n */\nexport function realZeroToOneExclusive(engine: Engine): number {\n  return uint53(engine) / SMALLEST_UNSAFE_INTEGER;\n}\n", "import { Engine } from \"../types\";\nimport { SMALLEST_UNSAFE_INTEGER } from \"../utils/constants\";\nimport { uint53Full } from \"./uint53Full\";\n\n/**\n * Returns a floating-point value within [0.0, 1.0]\n */\nexport function realZeroToOneInclusive(engine: Engine): number {\n  return uint53Full(engine) / SMALLEST_UNSAFE_INTEGER;\n}\n", "import { Distribution } from \"../types\";\nimport { add } from \"../utils/add\";\nimport { multiply } from \"../utils/multiply\";\nimport { realZeroToOneExclusive } from \"./realZeroToOneExclusive\";\nimport { realZeroToOneInclusive } from \"./realZeroToOneInclusive\";\n\n/**\n * Returns a floating-point value within [min, max) or [min, max]\n * @param min The minimum floating-point value, inclusive.\n * @param max The maximum floating-point value.\n * @param inclusive If true, `max` will be inclusive.\n */\nexport function real(\n  min: number,\n  max: number,\n  inclusive: boolean = false\n): Distribution {\n  if (!isFinite(min)) {\n    throw new RangeError(\"Expected min to be a finite number\");\n  } else if (!isFinite(max)) {\n    throw new RangeError(\"Expected max to be a finite number\");\n  }\n  return add(\n    multiply(\n      inclusive ? realZeroToOneInclusive : realZeroToOneExclusive,\n      max - min\n    ),\n    min\n  );\n}\n", "export const sliceArray = Array.prototype.slice;\n", "import { Engine } from \"../types\";\nimport { integer } from \"./integer\";\n\n/**\n * Shuffles an array in-place\n * @param engine The Engine to use when choosing random values\n * @param array The array to shuffle\n * @param downTo minimum index to shuffle. Only used internally.\n */\nexport function shuffle<T>(\n  engine: Engine,\n  array: T[],\n  downTo: number = 0\n): T[] {\n  const length = array.length;\n  if (length) {\n    for (let i = (length - 1) >>> 0; i > downTo; --i) {\n      const distribution = integer(0, i);\n      const j = distribution(engine);\n      if (i !== j) {\n        const tmp = array[i];\n        array[i] = array[j];\n        array[j] = tmp;\n      }\n    }\n  }\n  return array;\n}\n", "import { Engine } from \"../types\";\nimport { sliceArray } from \"../utils/sliceArray\";\nimport { shuffle } from \"./shuffle\";\n\n/**\n * From the population array, produce an array with sampleSize elements that\n * are randomly chosen without repeats.\n * @param engine The Engine to use when choosing random values\n * @param population An array that has items to choose a sample from\n * @param sampleSize The size of the result array\n */\nexport function sample<T>(\n  engine: Engine,\n  population: ArrayLike<T>,\n  sampleSize: number\n): T[] {\n  if (\n    sampleSize < 0 ||\n    sampleSize > population.length ||\n    !isFinite(sampleSize)\n  ) {\n    throw new RangeError(\n      \"Expected sampleSize to be within 0 and the length of the population\"\n    );\n  }\n\n  if (sampleSize === 0) {\n    return [];\n  }\n\n  const clone = sliceArray.call(population);\n  const length = clone.length;\n  if (length === sampleSize) {\n    return shuffle(engine, clone, 0);\n  }\n  const tailLength = length - sampleSize;\n  return shuffle(engine, clone, tailLength - 1).slice(tailLength);\n}\n", "export const stringRepeat = (() => {\n  try {\n    if ((\"x\" as any).repeat(3) === \"xxx\") {\n      return (pattern: string, count: number): string =>\n        (pattern as any).repeat(count);\n    }\n  } catch (_) {\n    // nothing to do here\n  }\n  return (pattern: string, count: number): string => {\n    let result = \"\";\n    while (count > 0) {\n      if (count & 1) {\n        result += pattern;\n      }\n      count >>= 1;\n      pattern += pattern;\n    }\n    return result;\n  };\n})();\n", "import { Engine } from \"../types\";\nimport { stringRepeat } from \"../utils/stringRepeat\";\n\nfunction zeroPad(text: string, zeroCount: number) {\n  return stringRepeat(\"0\", zeroCount - text.length) + text;\n}\n\n/**\n * Returns a Universally Unique Identifier Version 4.\n *\n * See http://en.wikipedia.org/wiki/Universally_unique_identifier\n */\nexport function uuid4(engine: Engine) {\n  const a = engine.next() >>> 0;\n  const b = engine.next() | 0;\n  const c = engine.next() | 0;\n  const d = engine.next() >>> 0;\n\n  return (\n    zeroPad(a.toString(16), 8) +\n    \"-\" +\n    zeroPad((b & 0xffff).toString(16), 4) +\n    \"-\" +\n    zeroPad((((b >> 4) & 0x0fff) | 0x4000).toString(16), 4) +\n    \"-\" +\n    zeroPad(((c & 0x3fff) | 0x8000).toString(16), 4) +\n    \"-\" +\n    zeroPad(((c >> 4) & 0xffff).toString(16), 4) +\n    zeroPad(d.toString(16), 8)\n  );\n}\n", "import { Engine } from \"../types\";\nimport { UINT32_SIZE } from \"../utils/constants\";\n\n/**\n * An int32-producing Engine that uses `Math.random()`\n */\nexport const nativeMath: Engine = {\n  next() {\n    return (Math.random() * UINT32_SIZE) | 0;\n  }\n};\n", "import { bool } from \"./distribution/bool\";\nimport { date } from \"./distribution/date\";\nimport { dice } from \"./distribution/dice\";\nimport { die } from \"./distribution/die\";\nimport { hex } from \"./distribution/hex\";\nimport { int32 } from \"./distribution/int32\";\nimport { int53 } from \"./distribution/int53\";\nimport { int53Full } from \"./distribution/int53Full\";\nimport { integer } from \"./distribution/integer\";\nimport { pick } from \"./distribution/pick\";\nimport { real } from \"./distribution/real\";\nimport { realZeroToOneExclusive } from \"./distribution/realZeroToOneExclusive\";\nimport { realZeroToOneInclusive } from \"./distribution/realZeroToOneInclusive\";\nimport { sample } from \"./distribution/sample\";\nimport { shuffle } from \"./distribution/shuffle\";\nimport { string } from \"./distribution/string\";\nimport { uint32 } from \"./distribution/uint32\";\nimport { uint53 } from \"./distribution/uint53\";\nimport { uint53Full } from \"./distribution/uint53Full\";\nimport { uuid4 } from \"./distribution/uuid4\";\nimport { nativeMath } from \"./engine/nativeMath\";\nimport { Engine } from \"./types\";\n\n// tslint:disable:unified-signatures\n\n/**\n * A wrapper around an Engine that provides easy-to-use methods for\n * producing values based on known distributions\n */\nexport class Random {\n  private readonly engine: Engine;\n\n  /**\n   * Creates a new Random wrapper\n   * @param engine The engine to use (defaults to a `Math.random`-based implementation)\n   */\n  constructor(engine: Engine = nativeMath) {\n    this.engine = engine;\n  }\n\n  /**\n   * Returns a value within [-0x80000000, 0x7fffffff]\n   */\n  public int32(): number {\n    return int32(this.engine);\n  }\n\n  /**\n   * Returns a value within [0, 0xffffffff]\n   */\n  public uint32(): number {\n    return uint32(this.engine);\n  }\n\n  /**\n   * Returns a value within [0, 0x1fffffffffffff]\n   */\n  public uint53(): number {\n    return uint53(this.engine);\n  }\n\n  /**\n   * Returns a value within [0, 0x20000000000000]\n   */\n  public uint53Full(): number {\n    return uint53Full(this.engine);\n  }\n\n  /**\n   * Returns a value within [-0x20000000000000, 0x1fffffffffffff]\n   */\n  public int53(): number {\n    return int53(this.engine);\n  }\n\n  /**\n   * Returns a value within [-0x20000000000000, 0x20000000000000]\n   */\n  public int53Full(): number {\n    return int53Full(this.engine);\n  }\n\n  /**\n   * Returns a value within [min, max]\n   * @param min The minimum integer value, inclusive. No less than -0x20000000000000.\n   * @param max The maximum integer value, inclusive. No greater than 0x20000000000000.\n   */\n  public integer(min: number, max: number): number {\n    return integer(min, max)(this.engine);\n  }\n\n  /**\n   * Returns a floating-point value within [0.0, 1.0]\n   */\n  public realZeroToOneInclusive(): number {\n    return realZeroToOneInclusive(this.engine);\n  }\n\n  /**\n   * Returns a floating-point value within [0.0, 1.0)\n   */\n  public realZeroToOneExclusive(): number {\n    return realZeroToOneExclusive(this.engine);\n  }\n\n  /**\n   * Returns a floating-point value within [min, max) or [min, max]\n   * @param min The minimum floating-point value, inclusive.\n   * @param max The maximum floating-point value.\n   * @param inclusive If true, `max` will be inclusive.\n   */\n  public real(min: number, max: number, inclusive: boolean = false): number {\n    return real(min, max, inclusive)(this.engine);\n  }\n\n  /**\n   * Returns a boolean with 50% probability of being true or false\n   */\n  public bool(): boolean;\n  /**\n   * Returns a boolean with the provided `percentage` of being true\n   * @param percentage A number within [0, 1] of how often the result should be `true`\n   */\n  public bool(percentage: number): boolean;\n  /**\n   * Returns a boolean with a probability of `numerator`/`denominator` of being true\n   * @param numerator The numerator of the probability\n   * @param denominator The denominator of the probability\n   */\n  public bool(numerator: number, denominator: number): boolean;\n  public bool(numerator?: number, denominator?: number): boolean {\n    return bool(numerator!, denominator!)(this.engine);\n  }\n\n  /**\n   * Return a random value within the provided `source` within the sliced\n   * bounds of `begin` and `end`.\n   * @param source an array of items to pick from\n   * @param begin the beginning slice index (defaults to `0`)\n   * @param end the ending slice index (defaults to `source.length`)\n   */\n  public pick<T>(source: ArrayLike<T>, begin?: number, end?: number): T {\n    return pick(this.engine, source, begin, end);\n  }\n\n  /**\n   * Shuffles an array in-place\n   * @param array The array to shuffle\n   */\n  public shuffle<T>(array: T[]): T[] {\n    return shuffle(this.engine, array);\n  }\n\n  /**\n   * From the population array, returns an array with sampleSize elements that\n   * are randomly chosen without repeats.\n   * @param population An array that has items to choose a sample from\n   * @param sampleSize The size of the result array\n   */\n  public sample<T>(population: ArrayLike<T>, sampleSize: number): T[] {\n    return sample(this.engine, population, sampleSize);\n  }\n\n  /**\n   * Returns a value within [1, sideCount]\n   * @param sideCount The number of sides of the die\n   */\n  public die(sideCount: number): number {\n    return die(sideCount)(this.engine);\n  }\n\n  /**\n   * Returns an array of length `dieCount` of values within [1, sideCount]\n   * @param sideCount The number of sides of each die\n   * @param dieCount The number of dice\n   */\n  public dice(sideCount: number, dieCount: number): number[] {\n    return dice(sideCount, dieCount)(this.engine);\n  }\n\n  /**\n   * Returns a Universally Unique Identifier Version 4.\n   *\n   * See http://en.wikipedia.org/wiki/Universally_unique_identifier\n   */\n  public uuid4(): string {\n    return uuid4(this.engine);\n  }\n\n  /**\n   * Returns a random string using numbers, uppercase and lowercase letters,\n   * `_`, and `-` of length `length`.\n   * @param length Length of the result string\n   */\n  public string(length: number): string;\n  /**\n   * Returns a random string using the provided string pool as the possible\n   * characters to choose from of length `length`.\n   * @param length Length of the result string\n   */\n  public string(length: number, pool: string): string;\n  public string(length: number, pool?: string): string {\n    return string(pool!)(this.engine, length);\n  }\n\n  /**\n   * Returns a random string comprised of numbers or the characters `abcdef`\n   * (or `ABCDEF`) of length `length`.\n   * @param length Length of the result string\n   * @param uppercase Whether the string should use `ABCDEF` instead of `abcdef`\n   */\n  public hex(length: number, uppercase?: boolean): string {\n    return hex(uppercase)(this.engine, length);\n  }\n\n  /**\n   * Returns a random `Date` within the inclusive range of [`start`, `end`].\n   * @param start The minimum `Date`\n   * @param end The maximum `Date`\n   */\n  public date(start: Date, end: Date): Date {\n    return date(start, end)(this.engine);\n  }\n}\n", "import { INT32_SIZE } from \"./constants\";\n\n/**\n * See https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Int32Array\n */\nconst I32Array: typeof Int32Array = (() => {\n  try {\n    const buffer = new ArrayBuffer(4);\n    const view = new Int32Array(buffer);\n    view[0] = INT32_SIZE;\n    if (view[0] === -INT32_SIZE) {\n      return Int32Array;\n    }\n  } catch (_) {\n    // nothing to do here\n  }\n  return (Array as unknown) as typeof Int32Array;\n})();\nexport { I32Array as Int32Array };\n", "import { Engine } from \"../types\";\nimport { Int32Array } from \"../utils/Int32Array\";\n\nlet data: Int32Array | null = null;\nconst COUNT = 128;\nlet index = COUNT;\n\n/**\n * An Engine that relies on the globally-available `crypto.getRandomValues`,\n * which is typically available in modern browsers.\n *\n * See https://developer.mozilla.org/en-US/docs/Web/API/Crypto/getRandomValues\n *\n * If unavailable or otherwise non-functioning, then `browserCrypto` will\n * likely `throw` on the first call to `next()`.\n */\nexport const browserCrypto: Engine = {\n  next() {\n    if (index >= COUNT) {\n      if (data === null) {\n        data = new Int32Array(COUNT);\n      }\n      crypto.getRandomValues(data);\n      index = 0;\n    }\n    return data![index++] | 0;\n  }\n};\n", "import { nativeMath } from \"../engine/nativeMath\";\nimport { Engine } from \"../types\";\n\n/**\n * Returns an array of random int32 values, based on current time\n * and a random number engine\n *\n * @param engine an Engine to pull random values from, default `nativeMath`\n * @param length the length of the Array, minimum 1, default 16\n */\nexport function createEntropy(\n  engine: Engine = nativeMath,\n  length: number = 16\n): number[] {\n  const array: number[] = [];\n  array.push(new Date().getTime() | 0);\n  for (let i = 1; i < length; ++i) {\n    array[i] = engine.next() | 0;\n  }\n  return array;\n}\n", "import { UINT32_MAX } from \"./constants\";\n\n/**\n * See https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Math/imul\n */\nexport const imul: (a: number, b: number) => number = (() => {\n  try {\n    if ((Math as any).imul(UINT32_MAX, 5) === -5) {\n      return (Math as any).imul;\n    }\n  } catch (_) {\n    // nothing to do here\n  }\n  const UINT16_MAX = 0xffff;\n  return (a: number, b: number) => {\n    const ah = (a >>> 16) & UINT16_MAX;\n    const al = a & UINT16_MAX;\n    const bh = (b >>> 16) & UINT16_MAX;\n    const bl = b & UINT16_MAX;\n    // the shift by 0 fixes the sign on the high part\n    // the final |0 converts the unsigned value into a signed value\n    return (al * bl + (((ah * bl + al * bh) << 16) >>> 0)) | 0;\n  };\n})();\n", "import { Engine } from \"../types\";\nimport { INT32_MAX, INT32_SIZE } from \"../utils/constants\";\nimport { createEntropy } from \"../utils/createEntropy\";\nimport { imul } from \"../utils/imul\";\nimport { Int32Array } from \"../utils/Int32Array\";\n\nconst ARRAY_SIZE = 624;\nconst ARRAY_MAX = ARRAY_SIZE - 1;\nconst M = 397;\nconst ARRAY_SIZE_MINUS_M = ARRAY_SIZE - M;\nconst A = 0x9908b0df;\n\n/**\n * An Engine that is a pseudorandom number generator using the Mersenne\n * Twister algorithm based on the prime 2**19937 − 1\n *\n * See http://en.wikipedia.org/wiki/Mersenne_twister\n */\nexport class MersenneTwister19937 implements Engine {\n  /**\n   * Returns a MersenneTwister19937 seeded with an initial int32 value\n   * @param initial the initial seed value\n   */\n  public static seed(initial: number): MersenneTwister19937 {\n    return new MersenneTwister19937().seed(initial);\n  }\n\n  /**\n   * Returns a MersenneTwister19937 seeded with zero or more int32 values\n   * @param source A series of int32 values\n   */\n  public static seedWithArray(source: ArrayLike<number>): MersenneTwister19937 {\n    return new MersenneTwister19937().seedWithArray(source);\n  }\n\n  /**\n   * Returns a MersenneTwister19937 seeded with the current time and\n   * a series of natively-generated random values\n   */\n  public static autoSeed(): MersenneTwister19937 {\n    return MersenneTwister19937.seedWithArray(createEntropy());\n  }\n\n  private readonly data = new Int32Array(ARRAY_SIZE);\n  private index = 0; // integer within [0, 624]\n  private uses = 0;\n\n  /**\n   * MersenneTwister19937 should not be instantiated directly.\n   * Instead, use the static methods `seed`, `seedWithArray`, or `autoSeed`.\n   */\n  private constructor() {}\n\n  /**\n   * Returns the next int32 value of the sequence\n   */\n  public next(): number {\n    if ((this.index | 0) >= ARRAY_SIZE) {\n      refreshData(this.data);\n      this.index = 0;\n    }\n\n    const value = this.data[this.index];\n    this.index = (this.index + 1) | 0;\n    this.uses += 1;\n    return temper(value) | 0;\n  }\n\n  /**\n   * Returns the number of times that the Engine has been used.\n   *\n   * This can be provided to an unused MersenneTwister19937 with the same\n   * seed, bringing it to the exact point that was left off.\n   */\n  public getUseCount(): number {\n    return this.uses;\n  }\n\n  /**\n   * Discards one or more items from the engine\n   * @param count The count of items to discard\n   */\n  public discard(count: number): this {\n    if (count <= 0) {\n      return this;\n    }\n    this.uses += count;\n    if ((this.index | 0) >= ARRAY_SIZE) {\n      refreshData(this.data);\n      this.index = 0;\n    }\n    while (count + this.index > ARRAY_SIZE) {\n      count -= ARRAY_SIZE - this.index;\n      refreshData(this.data);\n      this.index = 0;\n    }\n    this.index = (this.index + count) | 0;\n    return this;\n  }\n\n  private seed(initial: number): this {\n    let previous = 0;\n    this.data[0] = previous = initial | 0;\n\n    for (let i = 1; i < ARRAY_SIZE; i = (i + 1) | 0) {\n      this.data[i] = previous =\n        (imul(previous ^ (previous >>> 30), 0x6c078965) + i) | 0;\n    }\n    this.index = ARRAY_SIZE;\n    this.uses = 0;\n    return this;\n  }\n\n  private seedWithArray(source: ArrayLike<number>): this {\n    this.seed(0x012bd6aa);\n    seedWithArray(this.data, source);\n    return this;\n  }\n}\n\nfunction refreshData(data: Int32Array) {\n  let k = 0;\n  let tmp = 0;\n  for (; (k | 0) < ARRAY_SIZE_MINUS_M; k = (k + 1) | 0) {\n    tmp = (data[k] & INT32_SIZE) | (data[(k + 1) | 0] & INT32_MAX);\n    data[k] = data[(k + M) | 0] ^ (tmp >>> 1) ^ (tmp & 0x1 ? A : 0);\n  }\n\n  for (; (k | 0) < ARRAY_MAX; k = (k + 1) | 0) {\n    tmp = (data[k] & INT32_SIZE) | (data[(k + 1) | 0] & INT32_MAX);\n    data[k] =\n      data[(k - ARRAY_SIZE_MINUS_M) | 0] ^ (tmp >>> 1) ^ (tmp & 0x1 ? A : 0);\n  }\n\n  tmp = (data[ARRAY_MAX] & INT32_SIZE) | (data[0] & INT32_MAX);\n  data[ARRAY_MAX] = data[M - 1] ^ (tmp >>> 1) ^ (tmp & 0x1 ? A : 0);\n}\n\nfunction temper(value: number) {\n  value ^= value >>> 11;\n  value ^= (value << 7) & 0x9d2c5680;\n  value ^= (value << 15) & 0xefc60000;\n  return value ^ (value >>> 18);\n}\n\nfunction seedWithArray(data: Int32Array, source: ArrayLike<number>) {\n  let i = 1;\n  let j = 0;\n  const sourceLength = source.length;\n  let k = Math.max(sourceLength, ARRAY_SIZE) | 0;\n  let previous = data[0] | 0;\n  for (; (k | 0) > 0; --k) {\n    data[i] = previous =\n      ((data[i] ^ imul(previous ^ (previous >>> 30), 0x0019660d)) +\n        (source[j] | 0) +\n        (j | 0)) |\n      0;\n    i = (i + 1) | 0;\n    ++j;\n    if ((i | 0) > ARRAY_MAX) {\n      data[0] = data[ARRAY_MAX];\n      i = 1;\n    }\n    if (j >= sourceLength) {\n      j = 0;\n    }\n  }\n  for (k = ARRAY_MAX; (k | 0) > 0; --k) {\n    data[i] = previous =\n      ((data[i] ^ imul(previous ^ (previous >>> 30), 0x5d588b65)) - i) | 0;\n    i = (i + 1) | 0;\n    if ((i | 0) > ARRAY_MAX) {\n      data[0] = data[ARRAY_MAX];\n      i = 1;\n    }\n  }\n  data[0] = INT32_SIZE;\n}\n", "import { Engine } from \"../types\";\n\nlet data: Int32Array | null = null;\nconst COUNT = 128;\nlet index = COUNT;\n\n/**\n * An Engine that relies on the node-available\n * `require('crypto').randomBytes`, which has been available since 0.58.\n *\n * See https://nodejs.org/api/crypto.html#crypto_crypto_randombytes_size_callback\n *\n * If unavailable or otherwise non-functioning, then `nodeCrypto` will\n * likely `throw` on the first call to `next()`.\n */\nexport const nodeCrypto: Engine = {\n  next() {\n    if (index >= COUNT) {\n      data = new Int32Array(\n        new Int8Array(require(\"crypto\").randomBytes(4 * COUNT)).buffer\n      );\n      index = 0;\n    }\n    return data![index++] | 0;\n  }\n};\n", "import { Distribution } from \"../types\";\nimport { sliceArray } from \"../utils/sliceArray\";\nimport { integer } from \"./integer\";\n\n/**\n * Returns a Distribution to random value within the provided `source`\n * within the sliced bounds of `begin` and `end`.\n * @param source an array of items to pick from\n * @param begin the beginning slice index (defaults to `0`)\n * @param end the ending slice index (defaults to `source.length`)\n */\nexport function picker<T>(\n  source: ArrayLike<T>,\n  begin?: number,\n  end?: number\n): Distribution<T> {\n  const clone = sliceArray.call(source, begin, end);\n  if (clone.length === 0) {\n    throw new RangeError(`Cannot pick from a source with no items`);\n  }\n  const distribution = integer(0, clone.length - 1);\n  return engine => clone[distribution(engine)];\n}\n"], "names": ["Int32Array", "data", "COUNT", "index"], "mappings": ";;;;;;EAAO,IAAM,uBAAuB,GAAG,gBAAgB,CAAC;AACxD,EAAO,IAAM,oBAAoB,GAAG,uBAAuB,GAAG,CAAC,CAAC;AAChE,EAAO,IAAM,UAAU,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC;AACnC,EAAO,IAAM,WAAW,GAAG,UAAU,GAAG,CAAC,CAAC;AAC1C,EAAO,IAAM,UAAU,GAAG,WAAW,GAAG,CAAC,CAAC;AAC1C,EAAO,IAAM,SAAS,GAAG,UAAU,GAAG,CAAC,CAAC;AACxC,EAAO,IAAM,WAAW,GAAG,CAAC,IAAI,EAAE,CAAC;AACnC,EAAO,IAAM,UAAU,GAAG,WAAW,GAAG,CAAC,CAAC;;ECL1C;;;AAGA,WAAgB,KAAK,CAAC,MAAc;MAClC,OAAO,MAAM,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;EAC3B,CAAC;;WCLe,GAAG,CAAC,YAA0B,EAAE,MAAc;MAC5D,IAAI,MAAM,KAAK,CAAC,EAAE;UAChB,OAAO,YAAY,CAAC;OACrB;WAAM;UACL,OAAO,UAAA,MAAM,IAAI,OAAA,YAAY,CAAC,MAAM,CAAC,GAAG,MAAM,GAAA,CAAC;OAChD;EACH,CAAC;;ECAD;;;AAGA,WAAgB,KAAK,CAAC,MAAc;MAClC,IAAM,IAAI,GAAG,MAAM,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;MAC/B,IAAM,GAAG,GAAG,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;MAChC,QACE,CAAC,IAAI,GAAG,UAAU,IAAI,WAAW;UACjC,GAAG;WACF,IAAI,GAAG,WAAW,GAAG,CAAC,uBAAuB,GAAG,CAAC,CAAC,EACnD;EACJ,CAAC;;ECXD;;;AAGA,WAAgB,SAAS,CAAC,MAAc;MACtC,OAAO,IAAI,EAAE;UACX,IAAM,IAAI,GAAG,MAAM,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;UAC/B,IAAI,IAAI,GAAG,QAAQ,EAAE;cACnB,IAAI,CAAC,IAAI,GAAG,QAAQ,MAAM,QAAQ,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,GAAG,CAAC,MAAM,CAAC,EAAE;kBAC/D,OAAO,uBAAuB,CAAC;eAChC;WACF;eAAM;cACL,IAAM,GAAG,GAAG,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;cAChC,QACE,CAAC,IAAI,GAAG,UAAU,IAAI,WAAW;kBACjC,GAAG;mBACF,IAAI,GAAG,WAAW,GAAG,CAAC,uBAAuB,GAAG,CAAC,CAAC,EACnD;WACH;OACF;EACH,CAAC;;ECzBD;;;AAGA,WAAgB,MAAM,CAAC,MAAc;MACnC,OAAO,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;EAC7B,CAAC;;ECJD;;;AAGA,WAAgB,MAAM,CAAC,MAAc;MACnC,IAAM,IAAI,GAAG,MAAM,CAAC,IAAI,EAAE,GAAG,UAAU,CAAC;MACxC,IAAM,GAAG,GAAG,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;MAChC,OAAO,IAAI,GAAG,WAAW,GAAG,GAAG,CAAC;EAClC,CAAC;;ECFD;;;AAGA,WAAgB,UAAU,CAAC,MAAc;MACvC,OAAO,IAAI,EAAE;UACX,IAAM,IAAI,GAAG,MAAM,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;UAC/B,IAAI,IAAI,GAAG,WAAW,EAAE;cACtB,IAAI,CAAC,IAAI,GAAG,UAAU,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,GAAG,CAAC,MAAM,CAAC,EAAE;kBAC1D,OAAO,uBAAuB,CAAC;eAChC;WACF;eAAM;cACL,IAAM,GAAG,GAAG,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;cAChC,OAAO,CAAC,IAAI,GAAG,UAAU,IAAI,WAAW,GAAG,GAAG,CAAC;WAChD;OACF;EACH,CAAC;;ECLD,SAAS,oBAAoB,CAAC,KAAa;MACzC,OAAO,CAAC,CAAC,KAAK,GAAG,CAAC,IAAI,KAAK,MAAM,CAAC,CAAC;EACrC,CAAC;EAED,SAAS,OAAO,CAAC,OAAe;MAC9B,OAAO,UAAC,MAAc,IAAK,OAAA,MAAM,CAAC,IAAI,EAAE,GAAG,OAAO,GAAA,CAAC;EACrD,CAAC;EAED,SAAS,2BAA2B,CAAC,KAAa;MAChD,IAAM,aAAa,GAAG,KAAK,GAAG,CAAC,CAAC;MAChC,IAAM,OAAO,GAAG,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,GAAG,aAAa,CAAC,CAAC;MACxE,OAAO,UAAA,MAAM;UACX,IAAI,KAAK,GAAG,CAAC,CAAC;UACd,GAAG;cACD,KAAK,GAAG,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;WAC7B,QAAQ,KAAK,IAAI,OAAO,EAAE;UAC3B,OAAO,KAAK,GAAG,aAAa,CAAC;OAC9B,CAAC;EACJ,CAAC;EAED,SAAS,gBAAgB,CAAC,KAAa;MACrC,IAAI,oBAAoB,CAAC,KAAK,CAAC,EAAE;UAC/B,OAAO,OAAO,CAAC,KAAK,CAAC,CAAC;OACvB;WAAM;UACL,OAAO,2BAA2B,CAAC,KAAK,CAAC,CAAC;OAC3C;EACH,CAAC;EAED,SAAS,2BAA2B,CAAC,KAAa;MAChD,OAAO,CAAC,KAAK,GAAG,CAAC,MAAM,CAAC,CAAC;EAC3B,CAAC;EAED,SAAS,sBAAsB,CAAC,OAAe;MAC7C,OAAO,UAAA,MAAM;UACX,IAAM,IAAI,GAAG,MAAM,CAAC,IAAI,EAAE,GAAG,OAAO,CAAC;UACrC,IAAM,GAAG,GAAG,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;UAChC,OAAO,IAAI,GAAG,WAAW,GAAG,GAAG,CAAC;OACjC,CAAC;EACJ,CAAC;EAED,SAAS,yBAAyB,CAAC,aAAqB;MACtD,IAAM,OAAO,GACX,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,uBAAuB,GAAG,aAAa,CAAC,CAAC;MACtE,OAAO,UAAA,MAAM;UACX,IAAI,GAAG,GAAG,CAAC,CAAC;UACZ,GAAG;cACD,IAAM,IAAI,GAAG,MAAM,CAAC,IAAI,EAAE,GAAG,UAAU,CAAC;cACxC,IAAM,GAAG,GAAG,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;cAChC,GAAG,GAAG,IAAI,GAAG,WAAW,GAAG,GAAG,CAAC;WAChC,QAAQ,GAAG,IAAI,OAAO,EAAE;UACzB,OAAO,GAAG,GAAG,aAAa,CAAC;OAC5B,CAAC;EACJ,CAAC;EAED,SAAS,gBAAgB,CAAC,KAAa;MACrC,IAAM,aAAa,GAAG,KAAK,GAAG,CAAC,CAAC;MAChC,IAAI,2BAA2B,CAAC,aAAa,CAAC,EAAE;UAC9C,IAAM,SAAS,GAAG,CAAC,CAAC,aAAa,GAAG,WAAW,IAAI,CAAC,IAAI,CAAC,CAAC;UAC1D,IAAI,oBAAoB,CAAC,SAAS,CAAC,EAAE;cACnC,OAAO,sBAAsB,CAAC,SAAS,CAAC,CAAC;WAC1C;OACF;MACD,OAAO,yBAAyB,CAAC,aAAa,CAAC,CAAC;EAClD,CAAC;EAED,SAAS,4BAA4B,CAAC,GAAW,EAAE,GAAW;MAC5D,OAAO,UAAA,MAAM;UACX,IAAI,GAAG,GAAG,CAAC,CAAC;UACZ,GAAG;cACD,IAAM,IAAI,GAAG,MAAM,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;cAC/B,IAAM,GAAG,GAAG,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;cAChC,GAAG;kBACD,CAAC,IAAI,GAAG,UAAU,IAAI,WAAW;sBACjC,GAAG;uBACF,IAAI,GAAG,WAAW,GAAG,CAAC,uBAAuB,GAAG,CAAC,CAAC,CAAC;WACvD,QAAQ,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,EAAE;UACjC,OAAO,GAAG,CAAC;OACZ,CAAC;EACJ,CAAC;EAED;;;;;AAKA,WAAgB,OAAO,CAAC,GAAW,EAAE,GAAW;MAC9C,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;MACtB,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;MACtB,IAAI,GAAG,GAAG,CAAC,uBAAuB,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;UACpD,MAAM,IAAI,UAAU,CAClB,iCAA+B,CAAC,uBAAyB,CAC1D,CAAC;OACH;WAAM,IAAI,GAAG,GAAG,uBAAuB,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;UAC1D,MAAM,IAAI,UAAU,CAClB,gCAA8B,uBAAyB,CACxD,CAAC;OACH;MAED,IAAM,KAAK,GAAG,GAAG,GAAG,GAAG,CAAC;MACxB,IAAI,KAAK,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE;UAClC,OAAO,cAAM,OAAA,GAAG,GAAA,CAAC;OAClB;WAAM,IAAI,KAAK,KAAK,UAAU,EAAE;UAC/B,IAAI,GAAG,KAAK,CAAC,EAAE;cACb,OAAO,MAAM,CAAC;WACf;eAAM;cACL,OAAO,GAAG,CAAC,KAAK,EAAE,GAAG,GAAG,UAAU,CAAC,CAAC;WACrC;OACF;WAAM,IAAI,KAAK,GAAG,UAAU,EAAE;UAC7B,OAAO,GAAG,CAAC,gBAAgB,CAAC,KAAK,CAAC,EAAE,GAAG,CAAC,CAAC;OAC1C;WAAM,IAAI,KAAK,KAAK,oBAAoB,EAAE;UACzC,OAAO,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;OACzB;WAAM,IAAI,KAAK,GAAG,oBAAoB,EAAE;UACvC,OAAO,GAAG,CAAC,gBAAgB,CAAC,KAAK,CAAC,EAAE,GAAG,CAAC,CAAC;OAC1C;WAAM,IAAI,GAAG,GAAG,CAAC,GAAG,GAAG,KAAK,oBAAoB,EAAE;UACjD,OAAO,GAAG,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;OAC7B;WAAM,IACL,GAAG,KAAK,CAAC,uBAAuB;UAChC,GAAG,KAAK,uBAAuB,EAC/B;UACA,OAAO,SAAS,CAAC;OAClB;WAAM,IAAI,GAAG,KAAK,CAAC,uBAAuB,IAAI,GAAG,KAAK,oBAAoB,EAAE;UAC3E,OAAO,KAAK,CAAC;OACd;WAAM,IAAI,GAAG,KAAK,CAAC,oBAAoB,IAAI,GAAG,KAAK,uBAAuB,EAAE;UAC3E,OAAO,GAAG,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;OACtB;WAAM,IAAI,GAAG,KAAK,uBAAuB,EAAE;UAC1C,OAAO,GAAG,CAAC,4BAA4B,CAAC,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;OAC/D;WAAM;UACL,OAAO,4BAA4B,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;OAC/C;EACH,CAAC;;EC7ID,SAAS,cAAc,CAAC,MAAc;MACpC,OAAO,CAAC,MAAM,CAAC,IAAI,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC;EACnC,CAAC;EAED,SAAS,QAAQ,CACf,YAA0B,EAC1B,KAAa;MAEb,OAAO,UAAA,MAAM,IAAI,OAAA,YAAY,CAAC,MAAM,CAAC,GAAG,KAAK,GAAA,CAAC;EAChD,CAAC;EAED,SAAS,WAAW,CAAC,UAAkB;MACrC,IAAI,UAAU,IAAI,CAAC,EAAE;UACnB,OAAO,cAAM,OAAA,KAAK,GAAA,CAAC;OACpB;WAAM,IAAI,UAAU,IAAI,CAAC,EAAE;UAC1B,OAAO,cAAM,OAAA,IAAI,GAAA,CAAC;OACnB;WAAM;UACL,IAAM,MAAM,GAAG,UAAU,GAAG,WAAW,CAAC;UACxC,IAAI,MAAM,GAAG,CAAC,KAAK,CAAC,EAAE;cACpB,OAAO,QAAQ,CAAC,KAAK,EAAE,CAAC,MAAM,GAAG,UAAU,IAAI,CAAC,CAAC,CAAC;WACnD;eAAM;cACL,OAAO,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,uBAAuB,CAAC,CAAC,CAAC;WAC3E;OACF;EACH,CAAC;AAuBD,WAAgB,IAAI,CAClB,SAAkB,EAClB,WAAoB;MAEpB,IAAI,WAAW,IAAI,IAAI,EAAE;UACvB,IAAI,SAAS,IAAI,IAAI,EAAE;cACrB,OAAO,cAAc,CAAC;WACvB;UACD,OAAO,WAAW,CAAC,SAAS,CAAC,CAAC;OAC/B;WAAM;UACL,IAAI,SAAU,IAAI,CAAC,EAAE;cACnB,OAAO,cAAM,OAAA,KAAK,GAAA,CAAC;WACpB;eAAM,IAAI,SAAU,IAAI,WAAW,EAAE;cACpC,OAAO,cAAM,OAAA,IAAI,GAAA,CAAC;WACnB;UACD,OAAO,QAAQ,CAAC,OAAO,CAAC,CAAC,EAAE,WAAW,GAAG,CAAC,CAAC,EAAE,SAAU,CAAC,CAAC;OAC1D;EACH,CAAC;;ECnED;;;;;;AAMA,WAAgB,IAAI,CAAC,KAAW,EAAE,GAAS;MACzC,IAAM,YAAY,GAAG,OAAO,CAAC,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC;MAC3C,OAAO,UAAA,MAAM,IAAI,OAAA,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,GAAA,CAAC;EAClD,CAAC;;ECTD;;;;AAIA,WAAgB,GAAG,CAAC,SAAiB;MACnC,OAAO,OAAO,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC;EAC/B,CAAC;;ECND;;;;;;AAMA,WAAgB,IAAI,CAClB,SAAiB,EACjB,QAAgB;MAEhB,IAAM,YAAY,GAAG,GAAG,CAAC,SAAS,CAAC,CAAC;MACpC,OAAO,UAAA,MAAM;UACX,IAAM,MAAM,GAAG,EAAE,CAAC;UAClB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,EAAE,EAAE,CAAC,EAAE;cACjC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC;WACnC;UACD,OAAO,MAAM,CAAC;OACf,CAAC;EACJ,CAAC;;EClBD;EAEA;EACA,IAAM,mBAAmB,GACvB,kEAAkE,CAAC;AAcrE,WAAgB,MAAM,CAAC,IAAkC;MAAlC,qBAAA,EAAA,0BAAkC;MACvD,IAAM,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC;MAC/B,IAAI,CAAC,UAAU,EAAE;UACf,MAAM,IAAI,KAAK,CAAC,yCAAyC,CAAC,CAAC;OAC5D;MAED,IAAM,YAAY,GAAG,OAAO,CAAC,CAAC,EAAE,UAAU,GAAG,CAAC,CAAC,CAAC;MAChD,OAAO,UAAC,MAAM,EAAE,MAAM;UACpB,IAAI,MAAM,GAAG,EAAE,CAAC;UAChB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,EAAE,CAAC,EAAE;cAC/B,IAAM,CAAC,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC;cAC/B,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;WAC1B;UACD,OAAO,MAAM,CAAC;OACf,CAAC;EACJ,CAAC;;ECjCD,IAAM,cAAc,GAAG,kBAAkB,CAAC;EAC1C,IAAM,QAAQ,GAAG,MAAM,CAAC,cAAc,CAAC,CAAC;EACxC,IAAM,QAAQ,GAAG,MAAM,CAAC,cAAc,CAAC,WAAW,EAAE,CAAC,CAAC;EAEtD;;;;;;AAMA,WAAgB,GAAG,CAAC,SAAmB;MACrC,IAAI,SAAS,EAAE;UACb,OAAO,QAAQ,CAAC;OACjB;WAAM;UACL,OAAO,QAAQ,CAAC;OACjB;EACH,CAAC;;WCnBe,oBAAoB,CAAC,KAAa,EAAE,MAAc;MAChE,IAAI,KAAK,GAAG,CAAC,EAAE;UACb,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,GAAG,MAAM,EAAE,CAAC,CAAC,CAAC;OACpC;WAAM;UACL,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;OAChC;EACH,CAAC;;WCNe,SAAS,CAAC,KAAa;MACrC,IAAM,GAAG,GAAG,CAAC,KAAK,CAAC;MACnB,IAAI,GAAG,GAAG,CAAC,EAAE;UACX,OAAO,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;OACvB;WAAM;UACL,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;OACxB;EACH,CAAC;;ECFD;;;;;;;AAOA,WAAgB,IAAI,CAClB,MAAc,EACd,MAAoB,EACpB,KAAc,EACd,GAAY;MAEZ,IAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;MAC7B,IAAI,MAAM,KAAK,CAAC,EAAE;UAChB,MAAM,IAAI,UAAU,CAAC,iCAAiC,CAAC,CAAC;OACzD;MACD,IAAM,KAAK,GACT,KAAK,IAAI,IAAI,GAAG,CAAC,GAAG,oBAAoB,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,MAAM,CAAC,CAAC;MACrE,IAAM,MAAM,GACV,GAAG,KAAK,KAAK,CAAC,GAAG,MAAM,GAAG,oBAAoB,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE,MAAM,CAAC,CAAC;MACzE,IAAI,KAAK,IAAI,MAAM,EAAE;UACnB,MAAM,IAAI,UAAU,CAAC,gCAA8B,KAAK,aAAQ,MAAQ,CAAC,CAAC;OAC3E;MACD,IAAM,YAAY,GAAG,OAAO,CAAC,KAAK,EAAE,MAAM,GAAG,CAAC,CAAC,CAAC;MAChD,OAAO,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC;EACtC,CAAC;;WC7Be,QAAQ,CACtB,YAA0B,EAC1B,UAAkB;MAElB,IAAI,UAAU,KAAK,CAAC,EAAE;UACpB,OAAO,YAAY,CAAC;OACrB;WAAM,IAAI,UAAU,KAAK,CAAC,EAAE;UAC3B,OAAO,cAAM,OAAA,CAAC,GAAA,CAAC;OAChB;WAAM;UACL,OAAO,UAAA,MAAM,IAAI,OAAA,YAAY,CAAC,MAAM,CAAC,GAAG,UAAU,GAAA,CAAC;OACpD;EACH,CAAC;;ECTD;;;AAGA,WAAgB,sBAAsB,CAAC,MAAc;MACnD,OAAO,MAAM,CAAC,MAAM,CAAC,GAAG,uBAAuB,CAAC;EAClD,CAAC;;ECLD;;;AAGA,WAAgB,sBAAsB,CAAC,MAAc;MACnD,OAAO,UAAU,CAAC,MAAM,CAAC,GAAG,uBAAuB,CAAC;EACtD,CAAC;;ECHD;;;;;;AAMA,WAAgB,IAAI,CAClB,GAAW,EACX,GAAW,EACX,SAA0B;MAA1B,0BAAA,EAAA,iBAA0B;MAE1B,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;UAClB,MAAM,IAAI,UAAU,CAAC,oCAAoC,CAAC,CAAC;OAC5D;WAAM,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;UACzB,MAAM,IAAI,UAAU,CAAC,oCAAoC,CAAC,CAAC;OAC5D;MACD,OAAO,GAAG,CACR,QAAQ,CACN,SAAS,GAAG,sBAAsB,GAAG,sBAAsB,EAC3D,GAAG,GAAG,GAAG,CACV,EACD,GAAG,CACJ,CAAC;EACJ,CAAC;;EC7BM,IAAM,UAAU,GAAG,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC;;ECGhD;;;;;;AAMA,WAAgB,OAAO,CACrB,MAAc,EACd,KAAU,EACV,MAAkB;MAAlB,uBAAA,EAAA,UAAkB;MAElB,IAAM,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;MAC5B,IAAI,MAAM,EAAE;UACV,KAAK,IAAI,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,EAAE,CAAC,EAAE;cAChD,IAAM,YAAY,GAAG,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;cACnC,IAAM,CAAC,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC;cAC/B,IAAI,CAAC,KAAK,CAAC,EAAE;kBACX,IAAM,GAAG,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;kBACrB,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;kBACpB,KAAK,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;eAChB;WACF;OACF;MACD,OAAO,KAAK,CAAC;EACf,CAAC;;ECvBD;;;;;;;AAOA,WAAgB,MAAM,CACpB,MAAc,EACd,UAAwB,EACxB,UAAkB;MAElB,IACE,UAAU,GAAG,CAAC;UACd,UAAU,GAAG,UAAU,CAAC,MAAM;UAC9B,CAAC,QAAQ,CAAC,UAAU,CAAC,EACrB;UACA,MAAM,IAAI,UAAU,CAClB,qEAAqE,CACtE,CAAC;OACH;MAED,IAAI,UAAU,KAAK,CAAC,EAAE;UACpB,OAAO,EAAE,CAAC;OACX;MAED,IAAM,KAAK,GAAG,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;MAC1C,IAAM,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;MAC5B,IAAI,MAAM,KAAK,UAAU,EAAE;UACzB,OAAO,OAAO,CAAC,MAAM,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC;OAClC;MACD,IAAM,UAAU,GAAG,MAAM,GAAG,UAAU,CAAC;MACvC,OAAO,OAAO,CAAC,MAAM,EAAE,KAAK,EAAE,UAAU,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;EAClE,CAAC;;ECrCM,IAAM,YAAY,GAAG,CAAC;MAC3B,IAAI;UACF,IAAK,GAAW,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,KAAK,EAAE;cACpC,OAAO,UAAC,OAAe,EAAE,KAAa;kBACpC,OAAC,OAAe,CAAC,MAAM,CAAC,KAAK,CAAC;eAAA,CAAC;WAClC;OACF;MAAC,OAAO,CAAC,EAAE;;OAEX;MACD,OAAO,UAAC,OAAe,EAAE,KAAa;UACpC,IAAI,MAAM,GAAG,EAAE,CAAC;UAChB,OAAO,KAAK,GAAG,CAAC,EAAE;cAChB,IAAI,KAAK,GAAG,CAAC,EAAE;kBACb,MAAM,IAAI,OAAO,CAAC;eACnB;cACD,KAAK,KAAK,CAAC,CAAC;cACZ,OAAO,IAAI,OAAO,CAAC;WACpB;UACD,OAAO,MAAM,CAAC;OACf,CAAC;EACJ,CAAC,GAAG,CAAC;;ECjBL,SAAS,OAAO,CAAC,IAAY,EAAE,SAAiB;MAC9C,OAAO,YAAY,CAAC,GAAG,EAAE,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC;EAC3D,CAAC;EAED;;;;;AAKA,WAAgB,KAAK,CAAC,MAAc;MAClC,IAAM,CAAC,GAAG,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;MAC9B,IAAM,CAAC,GAAG,MAAM,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;MAC5B,IAAM,CAAC,GAAG,MAAM,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;MAC5B,IAAM,CAAC,GAAG,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;MAE9B,QACE,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UAC1B,GAAG;UACH,OAAO,CAAC,CAAC,CAAC,GAAG,MAAM,EAAE,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UACrC,GAAG;UACH,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,MAAM,IAAI,MAAM,EAAE,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UACvD,GAAG;UACH,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,MAAM,IAAI,MAAM,EAAE,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UAChD,GAAG;UACH,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,MAAM,EAAE,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UAC5C,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAC1B;EACJ,CAAC;;EC3BD;;;AAGA,MAAa,UAAU,GAAW;MAChC,IAAI;UACF,OAAO,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,WAAW,IAAI,CAAC,CAAC;OAC1C;GACF;;ECaD;EAEA;;;;AAIA;;;;;MAOE,gBAAY,MAA2B;UAA3B,uBAAA,EAAA,mBAA2B;UACrC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;OACtB;;;;MAKM,sBAAK,GAAZ;UACE,OAAO,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;OAC3B;;;;MAKM,uBAAM,GAAb;UACE,OAAO,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;OAC5B;;;;MAKM,uBAAM,GAAb;UACE,OAAO,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;OAC5B;;;;MAKM,2BAAU,GAAjB;UACE,OAAO,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;OAChC;;;;MAKM,sBAAK,GAAZ;UACE,OAAO,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;OAC3B;;;;MAKM,0BAAS,GAAhB;UACE,OAAO,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;OAC/B;;;;;;MAOM,wBAAO,GAAd,UAAe,GAAW,EAAE,GAAW;UACrC,OAAO,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;OACvC;;;;MAKM,uCAAsB,GAA7B;UACE,OAAO,sBAAsB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;OAC5C;;;;MAKM,uCAAsB,GAA7B;UACE,OAAO,sBAAsB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;OAC5C;;;;;;;MAQM,qBAAI,GAAX,UAAY,GAAW,EAAE,GAAW,EAAE,SAA0B;UAA1B,0BAAA,EAAA,iBAA0B;UAC9D,OAAO,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,SAAS,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;OAC/C;MAiBM,qBAAI,GAAX,UAAY,SAAkB,EAAE,WAAoB;UAClD,OAAO,IAAI,CAAC,SAAU,EAAE,WAAY,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;OACpD;;;;;;;;MASM,qBAAI,GAAX,UAAe,MAAoB,EAAE,KAAc,EAAE,GAAY;UAC/D,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC;OAC9C;;;;;MAMM,wBAAO,GAAd,UAAkB,KAAU;UAC1B,OAAO,OAAO,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;OACpC;;;;;;;MAQM,uBAAM,GAAb,UAAiB,UAAwB,EAAE,UAAkB;UAC3D,OAAO,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC;OACpD;;;;;MAMM,oBAAG,GAAV,UAAW,SAAiB;UAC1B,OAAO,GAAG,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;OACpC;;;;;;MAOM,qBAAI,GAAX,UAAY,SAAiB,EAAE,QAAgB;UAC7C,OAAO,IAAI,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;OAC/C;;;;;;MAOM,sBAAK,GAAZ;UACE,OAAO,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;OAC3B;MAcM,uBAAM,GAAb,UAAc,MAAc,EAAE,IAAa;UACzC,OAAO,MAAM,CAAC,IAAK,CAAC,CAAC,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;OAC3C;;;;;;;MAQM,oBAAG,GAAV,UAAW,MAAc,EAAE,SAAmB;UAC5C,OAAO,GAAG,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;OAC5C;;;;;;MAOM,qBAAI,GAAX,UAAY,KAAW,EAAE,GAAS;UAChC,OAAO,IAAI,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;OACtC;MACH,aAAC;EAAD,CAAC;;EC7ND;;;EAGA,IAAM,QAAQ,GAAsB,CAAC;MACnC,IAAI;UACF,IAAM,MAAM,GAAG,IAAI,WAAW,CAAC,CAAC,CAAC,CAAC;UAClC,IAAM,IAAI,GAAG,IAAI,UAAU,CAAC,MAAM,CAAC,CAAC;UACpC,IAAI,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC;UACrB,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,UAAU,EAAE;cAC3B,OAAO,UAAU,CAAC;WACnB;OACF;MAAC,OAAO,CAAC,EAAE;;OAEX;MACD,OAAQ,KAAsC,CAAC;EACjD,CAAC,GAAG,CAAC;;ECdL,IAAI,IAAI,GAAsB,IAAI,CAAC;EACnC,IAAM,KAAK,GAAG,GAAG,CAAC;EAClB,IAAI,KAAK,GAAG,KAAK,CAAC;EAElB;;;;;;;;;AASA,MAAa,aAAa,GAAW;MACnC,IAAI;UACF,IAAI,KAAK,IAAI,KAAK,EAAE;cAClB,IAAI,IAAI,KAAK,IAAI,EAAE;kBACjB,IAAI,GAAG,IAAIA,QAAU,CAAC,KAAK,CAAC,CAAC;eAC9B;cACD,MAAM,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;cAC7B,KAAK,GAAG,CAAC,CAAC;WACX;UACD,OAAO,IAAK,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC;OAC3B;GACF;;ECxBD;;;;;;;AAOA,WAAgB,aAAa,CAC3B,MAA2B,EAC3B,MAAmB;MADnB,uBAAA,EAAA,mBAA2B;MAC3B,uBAAA,EAAA,WAAmB;MAEnB,IAAM,KAAK,GAAa,EAAE,CAAC;MAC3B,KAAK,CAAC,IAAI,CAAC,IAAI,IAAI,EAAE,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC;MACrC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,EAAE,CAAC,EAAE;UAC/B,KAAK,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;OAC9B;MACD,OAAO,KAAK,CAAC;EACf,CAAC;;EClBD;;;AAGA,EAAO,IAAM,IAAI,GAAqC,CAAC;MACrD,IAAI;UACF,IAAK,IAAY,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE;cAC5C,OAAQ,IAAY,CAAC,IAAI,CAAC;WAC3B;OACF;MAAC,OAAO,CAAC,EAAE;;OAEX;MACD,IAAM,UAAU,GAAG,MAAM,CAAC;MAC1B,OAAO,UAAC,CAAS,EAAE,CAAS;UAC1B,IAAM,EAAE,GAAG,CAAC,CAAC,KAAK,EAAE,IAAI,UAAU,CAAC;UACnC,IAAM,EAAE,GAAG,CAAC,GAAG,UAAU,CAAC;UAC1B,IAAM,EAAE,GAAG,CAAC,CAAC,KAAK,EAAE,IAAI,UAAU,CAAC;UACnC,IAAM,EAAE,GAAG,CAAC,GAAG,UAAU,CAAC;;;UAG1B,OAAO,CAAC,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC;OAC5D,CAAC;EACJ,CAAC,GAAG,CAAC;;ECjBL,IAAM,UAAU,GAAG,GAAG,CAAC;EACvB,IAAM,SAAS,GAAG,UAAU,GAAG,CAAC,CAAC;EACjC,IAAM,CAAC,GAAG,GAAG,CAAC;EACd,IAAM,kBAAkB,GAAG,UAAU,GAAG,CAAC,CAAC;EAC1C,IAAM,CAAC,GAAG,UAAU,CAAC;EAErB;;;;;;AAMA;;;;;MAiCE;UARiB,SAAI,GAAG,IAAIA,QAAU,CAAC,UAAU,CAAC,CAAC;UAC3C,UAAK,GAAG,CAAC,CAAC;UACV,SAAI,GAAG,CAAC,CAAC;OAMO;;;;;MA5BV,yBAAI,GAAlB,UAAmB,OAAe;UAChC,OAAO,IAAI,oBAAoB,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;OACjD;;;;;MAMa,kCAAa,GAA3B,UAA4B,MAAyB;UACnD,OAAO,IAAI,oBAAoB,EAAE,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;OACzD;;;;;MAMa,6BAAQ,GAAtB;UACE,OAAO,oBAAoB,CAAC,aAAa,CAAC,aAAa,EAAE,CAAC,CAAC;OAC5D;;;;MAeM,mCAAI,GAAX;UACE,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC,KAAK,UAAU,EAAE;cAClC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;cACvB,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;WAChB;UAED,IAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;UACpC,IAAI,CAAC,KAAK,GAAG,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC,IAAI,CAAC,CAAC;UAClC,IAAI,CAAC,IAAI,IAAI,CAAC,CAAC;UACf,OAAO,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;OAC1B;;;;;;;MAQM,0CAAW,GAAlB;UACE,OAAO,IAAI,CAAC,IAAI,CAAC;OAClB;;;;;MAMM,sCAAO,GAAd,UAAe,KAAa;UAC1B,IAAI,KAAK,IAAI,CAAC,EAAE;cACd,OAAO,IAAI,CAAC;WACb;UACD,IAAI,CAAC,IAAI,IAAI,KAAK,CAAC;UACnB,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC,KAAK,UAAU,EAAE;cAClC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;cACvB,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;WAChB;UACD,OAAO,KAAK,GAAG,IAAI,CAAC,KAAK,GAAG,UAAU,EAAE;cACtC,KAAK,IAAI,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC;cACjC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;cACvB,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;WAChB;UACD,IAAI,CAAC,KAAK,GAAG,CAAC,IAAI,CAAC,KAAK,GAAG,KAAK,IAAI,CAAC,CAAC;UACtC,OAAO,IAAI,CAAC;OACb;MAEO,mCAAI,GAAZ,UAAa,OAAe;UAC1B,IAAI,QAAQ,GAAG,CAAC,CAAC;UACjB,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,QAAQ,GAAG,OAAO,GAAG,CAAC,CAAC;UAEtC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;cAC/C,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,QAAQ;kBACrB,CAAC,IAAI,CAAC,QAAQ,IAAI,QAAQ,KAAK,EAAE,CAAC,EAAE,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;WAC5D;UACD,IAAI,CAAC,KAAK,GAAG,UAAU,CAAC;UACxB,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC;UACd,OAAO,IAAI,CAAC;OACb;MAEO,4CAAa,GAArB,UAAsB,MAAyB;UAC7C,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;UACtB,aAAa,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;UACjC,OAAO,IAAI,CAAC;OACb;MACH,2BAAC;EAAD,CAAC,IAAA;EAED,SAAS,WAAW,CAAC,IAAgB;MACnC,IAAI,CAAC,GAAG,CAAC,CAAC;MACV,IAAI,GAAG,GAAG,CAAC,CAAC;MACZ,OAAO,CAAC,CAAC,GAAG,CAAC,IAAI,kBAAkB,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;UACpD,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,UAAU,KAAK,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC;UAC/D,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,IAAI,GAAG,KAAK,CAAC,CAAC,IAAI,GAAG,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;OACjE;MAED,OAAO,CAAC,CAAC,GAAG,CAAC,IAAI,SAAS,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;UAC3C,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,UAAU,KAAK,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC;UAC/D,IAAI,CAAC,CAAC,CAAC;cACL,IAAI,CAAC,CAAC,CAAC,GAAG,kBAAkB,IAAI,CAAC,CAAC,IAAI,GAAG,KAAK,CAAC,CAAC,IAAI,GAAG,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;OAC1E;MAED,GAAG,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,UAAU,KAAK,IAAI,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC;MAC7D,IAAI,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,GAAG,KAAK,CAAC,CAAC,IAAI,GAAG,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;EACpE,CAAC;EAED,SAAS,MAAM,CAAC,KAAa;MAC3B,KAAK,IAAI,KAAK,KAAK,EAAE,CAAC;MACtB,KAAK,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI,UAAU,CAAC;MACnC,KAAK,IAAI,CAAC,KAAK,IAAI,EAAE,IAAI,UAAU,CAAC;MACpC,OAAO,KAAK,IAAI,KAAK,KAAK,EAAE,CAAC,CAAC;EAChC,CAAC;EAED,SAAS,aAAa,CAAC,IAAgB,EAAE,MAAyB;MAChE,IAAI,CAAC,GAAG,CAAC,CAAC;MACV,IAAI,CAAC,GAAG,CAAC,CAAC;MACV,IAAM,YAAY,GAAG,MAAM,CAAC,MAAM,CAAC;MACnC,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE,UAAU,CAAC,GAAG,CAAC,CAAC;MAC/C,IAAI,QAAQ,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;MAC3B,OAAO,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,EAAE;UACvB,IAAI,CAAC,CAAC,CAAC,GAAG,QAAQ;cAChB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,QAAQ,IAAI,QAAQ,KAAK,EAAE,CAAC,EAAE,UAAU,CAAC;mBACvD,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;mBACd,CAAC,GAAG,CAAC,CAAC;kBACT,CAAC,CAAC;UACJ,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;UAChB,EAAE,CAAC,CAAC;UACJ,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,SAAS,EAAE;cACvB,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC;cAC1B,CAAC,GAAG,CAAC,CAAC;WACP;UACD,IAAI,CAAC,IAAI,YAAY,EAAE;cACrB,CAAC,GAAG,CAAC,CAAC;WACP;OACF;MACD,KAAK,CAAC,GAAG,SAAS,EAAE,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,EAAE;UACpC,IAAI,CAAC,CAAC,CAAC,GAAG,QAAQ;cAChB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,QAAQ,IAAI,QAAQ,KAAK,EAAE,CAAC,EAAE,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;UACvE,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;UAChB,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,SAAS,EAAE;cACvB,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC;cAC1B,CAAC,GAAG,CAAC,CAAC;WACP;OACF;MACD,IAAI,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC;EACvB,CAAC;;EC/KD,IAAIC,MAAI,GAAsB,IAAI,CAAC;EACnC,IAAMC,OAAK,GAAG,GAAG,CAAC;EAClB,IAAIC,OAAK,GAAGD,OAAK,CAAC;EAElB;;;;;;;;;AASA,MAAa,UAAU,GAAW;MAChC,IAAI;UACF,IAAIC,OAAK,IAAID,OAAK,EAAE;cAClBD,MAAI,GAAG,IAAI,UAAU,CACnB,IAAI,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,WAAW,CAAC,CAAC,GAAGC,OAAK,CAAC,CAAC,CAAC,MAAM,CAC/D,CAAC;cACFC,OAAK,GAAG,CAAC,CAAC;WACX;UACD,OAAOF,MAAK,CAACE,OAAK,EAAE,CAAC,GAAG,CAAC,CAAC;OAC3B;GACF;;ECrBD;;;;;;;AAOA,WAAgB,MAAM,CACpB,MAAoB,EACpB,KAAc,EACd,GAAY;MAEZ,IAAM,KAAK,GAAG,UAAU,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC;MAClD,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;UACtB,MAAM,IAAI,UAAU,CAAC,yCAAyC,CAAC,CAAC;OACjE;MACD,IAAM,YAAY,GAAG,OAAO,CAAC,CAAC,EAAE,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;MAClD,OAAO,UAAA,MAAM,IAAI,OAAA,KAAK,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,GAAA,CAAC;EAC/C,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}